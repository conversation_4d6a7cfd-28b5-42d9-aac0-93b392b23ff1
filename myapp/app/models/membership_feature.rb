class MembershipFeature < ApplicationRecord
  # Associations
  has_many :tier_features, dependent: :destroy
  has_many :membership_tiers, through: :tier_features
  has_many :usage_trackers, dependent: :destroy

  # Enums
  enum feature_type: {
    countable: 0,      # Features with usage limits (courses, consultations)
    boolean: 1,        # Features that are either included or not (emergency, AI insights)
    time_based: 2      # Features with time-based limits (recording access days)
  }

  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :feature_key, presence: true, uniqueness: true,
            format: { with: /\A[a-z_]+\z/, message: "must contain only lowercase letters and underscores" }
  validates :description, presence: true
  validates :feature_type, presence: true
  validates :category, presence: true

  # Scopes
  scope :by_category, ->(category) { where(category: category) }
  scope :countable_features, -> { where(feature_type: :countable) }
  scope :boolean_features, -> { where(feature_type: :boolean) }

  # Instance methods
  def countable?
    feature_type == 'countable'
  end

  def boolean?
    feature_type == 'boolean'
  end

  def time_based?
    feature_type == 'time_based'
  end
end
