class MedicalLicense < ApplicationRecord
  belongs_to :doctor

  # Enums
  enum status: {
    active: 0,
    expired: 1,
    suspended: 2,
    revoked: 3,
    pending: 4
  }

  # Validations
  validates :license_number, presence: true, uniqueness: true
  validates :issuing_authority, presence: true
  validates :issue_date, presence: true
  validates :expiry_date, presence: true
  validates :license_type, presence: true
  validates :status, presence: true
  validate :expiry_after_issue_date
  validate :license_not_expired_if_active

  # Callbacks
  before_save :check_expiry_status

  # Scopes
  scope :active, -> { where(status: :active) }
  scope :expired, -> { where(status: :expired) }
  scope :expiring_soon, ->(days = 30) { active.where('expiry_date <= ?', days.days.from_now) }
  scope :by_type, ->(type) { where(license_type: type) }

  # Instance methods
  def active?
    status == 'active' && !expired?
  end

  def expired?
    expiry_date < Date.current
  end

  def expires_soon?(days = 30)
    active? && expiry_date <= days.days.from_now.to_date
  end

  def days_until_expiry
    return 0 if expired?
    (expiry_date - Date.current).to_i
  end

  def valid_for_practice?
    active? && !expired?
  end

  private

  def expiry_after_issue_date
    return unless issue_date && expiry_date

    if expiry_date <= issue_date
      errors.add(:expiry_date, "must be after issue date")
    end
  end

  def license_not_expired_if_active
    return unless status == 'active' && expired?

    errors.add(:status, "cannot be active if license is expired")
  end

  def check_expiry_status
    if expired? && status == 'active'
      self.status = 'expired'
    end
  end
end
