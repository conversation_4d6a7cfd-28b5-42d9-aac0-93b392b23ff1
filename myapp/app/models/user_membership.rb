class UserMembership < ApplicationRecord
  belongs_to :user
  belongs_to :membership_tier

  # Enums
  enum status: {
    inactive: 0,
    active: 1,
    expired: 2,
    cancelled: 3,
    suspended: 4
  }

  # Validations
  validates :status, presence: true
  validates :starts_at, presence: true
  validates :expires_at, presence: true
  validates :auto_renewal, inclusion: { in: [true, false] }
  validate :expires_after_starts
  validate :one_active_membership_per_user

  # Callbacks
  before_validation :set_default_dates, on: :create
  after_update :handle_status_change

  # Scopes
  scope :active, -> { where(status: :active) }
  scope :expired, -> { where(status: :expired) }
  scope :current, -> { where('starts_at <= ? AND expires_at >= ?', Time.current, Time.current) }
  scope :expiring_soon, ->(days = 7) { active.where('expires_at <= ?', days.days.from_now) }

  # Instance methods
  def active?
    status == 'active' && current?
  end

  def current?
    Time.current.between?(starts_at, expires_at)
  end

  def expired?
    expires_at < Time.current
  end

  def days_remaining
    return 0 if expired?
    ((expires_at - Time.current) / 1.day).ceil
  end

  def trial_period?
    membership_tier.has_trial? &&
    starts_at + membership_tier.trial_days.days >= Time.current
  end

  def renew!(new_expires_at = nil)
    new_expires_at ||= expires_at + membership_tier.billing_cycle.months

    update!(
      expires_at: new_expires_at,
      status: :active
    )
  end

  def cancel!
    update!(
      status: :cancelled,
      auto_renewal: false
    )
  end

  def suspend!
    update!(status: :suspended)
  end

  def reactivate!
    update!(status: :active) if suspended?
  end

  private

  def expires_after_starts
    return unless starts_at && expires_at

    if expires_at <= starts_at
      errors.add(:expires_at, "must be after start date")
    end
  end

  def one_active_membership_per_user
    return unless user_id && active?

    existing = UserMembership.active.where(user_id: user_id)
    existing = existing.where.not(id: id) if persisted?

    if existing.exists?
      errors.add(:base, "User can only have one active membership")
    end
  end

  def set_default_dates
    return if starts_at.present? && expires_at.present?

    self.starts_at ||= Time.current
    self.expires_at ||= starts_at + membership_tier.billing_cycle.months
  end

  def handle_status_change
    if saved_change_to_status? && expired?
      # Handle expired membership logic here
      # Could trigger notifications, downgrade features, etc.
    end
  end
end
