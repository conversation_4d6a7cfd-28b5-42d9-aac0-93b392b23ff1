class Patient < ApplicationRecord
  belongs_to :user
  has_many :appointments, dependent: :destroy
  has_many :consultations, dependent: :destroy
  has_many :prescriptions, dependent: :destroy
  has_one :health_profile, dependent: :destroy
  has_many :cycle_trackers, dependent: :destroy
  has_many :pregnancy_trackers, dependent: :destroy
  has_many :health_metrics, dependent: :destroy
  has_many :ai_health_insights, dependent: :destroy
  has_many :risk_assessments, dependent: :destroy
  has_many :medical_documents, dependent: :destroy
  has_many :course_enrollments, dependent: :destroy
  has_many :courses, through: :course_enrollments
  has_many :video_progresses, dependent: :destroy

  # Enums
  enum smoking_status: {
    non_smoker: 0,
    former_smoker: 1,
    current_smoker: 2
  }

  enum alcohol_consumption: {
    never: 0,
    occasionally: 1,
    regularly: 2
  }

  # Validations
  validates :medical_record_number, presence: true, uniqueness: true
  validates :emergency_contact_name, presence: true
  validates :emergency_contact_phone, presence: true, format: { with: /\A\+?[1-9]\d{1,14}\z/ }
  validates :blood_type, inclusion: { in: %w[A+ A- B+ B- AB+ AB- O+ O-] }, allow_blank: true
  validates :pregnancy_count, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true
  validates :birth_count, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true
  validates :user_id, uniqueness: true

  # Callbacks
  before_validation :generate_medical_record_number, on: :create

  # Instance methods
  def age
    return nil unless user.birth_date
    ((Date.current - user.birth_date) / 365.25).floor
  end

  def current_pregnancy
    pregnancy_trackers.where(active: true).first
  end

  def is_pregnant?
    current_pregnancy.present?
  end

  def latest_cycle
    cycle_trackers.order(cycle_start_date: :desc).first
  end

  private

  def generate_medical_record_number
    return if medical_record_number.present?

    loop do
      self.medical_record_number = "MRN#{SecureRandom.random_number(1000000..9999999)}"
      break unless Patient.exists?(medical_record_number: medical_record_number)
    end
  end
end
