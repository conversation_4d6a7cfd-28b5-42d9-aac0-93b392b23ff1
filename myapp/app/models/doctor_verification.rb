class DoctorVerification < ApplicationRecord
  belongs_to :doctor

  # Enums
  enum status: {
    pending: 0,
    under_review: 1,
    approved: 2,
    rejected: 3,
    requires_additional_info: 4
  }

  # Validations
  validates :verification_type, presence: true
  validates :status, presence: true
  validates :submitted_at, presence: true
  validates :doctor_id, uniqueness: { scope: :verification_type }

  # Serialization
  serialize :documents, coder: JSON

  # Callbacks
  before_validation :set_submitted_at, on: :create
  after_update :update_doctor_verification_status

  # Scopes
  scope :pending, -> { where(status: :pending) }
  scope :under_review, -> { where(status: :under_review) }
  scope :approved, -> { where(status: :approved) }
  scope :rejected, -> { where(status: :rejected) }
  scope :by_type, ->(type) { where(verification_type: type) }

  # Instance methods
  def approved?
    status == 'approved'
  end

  def rejected?
    status == 'rejected'
  end

  def pending_review?
    %w[pending under_review requires_additional_info].include?(status)
  end

  def reviewed?
    reviewed_at.present?
  end

  def review_duration
    return nil unless reviewed_at && submitted_at
    ((reviewed_at - submitted_at) / 1.day).round(1)
  end

  def approve!(reviewer_notes = nil)
    update!(
      status: :approved,
      reviewed_at: Time.current,
      reviewer_notes: reviewer_notes
    )
  end

  def reject!(reviewer_notes)
    update!(
      status: :rejected,
      reviewed_at: Time.current,
      reviewer_notes: reviewer_notes
    )
  end

  def request_additional_info!(reviewer_notes)
    update!(
      status: :requires_additional_info,
      reviewed_at: Time.current,
      reviewer_notes: reviewer_notes
    )
  end

  private

  def set_submitted_at
    self.submitted_at ||= Time.current
  end

  def update_doctor_verification_status
    return unless saved_change_to_status?

    # Update doctor's overall verification status based on all verifications
    if approved?
      # Check if all required verifications are approved
      required_verifications = %w[identity education license]
      all_approved = required_verifications.all? do |type|
        doctor.doctor_verifications.where(verification_type: type, status: :approved).exists?
      end

      if all_approved
        doctor.update!(verification_status: :verified)
      end
    elsif rejected?
      doctor.update!(verification_status: :rejected)
    end
  end
end
