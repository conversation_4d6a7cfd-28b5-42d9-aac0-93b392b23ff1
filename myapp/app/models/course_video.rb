class CourseVideo < ApplicationRecord
  belongs_to :course_module
  has_one :course, through: :course_module
  has_many :video_progresses, dependent: :destroy

  # Validations
  validates :title, presence: true, length: { minimum: 3, maximum: 200 }
  validates :video_url, presence: true
  validates :duration_seconds, presence: true, numericality: { greater_than: 0 }
  validates :sort_order, presence: true, uniqueness: { scope: :course_module_id }

  # Callbacks
  after_save :update_module_duration
  after_destroy :update_module_duration

  # Scopes
  scope :published, -> { where(is_published: true) }
  scope :preview, -> { where(is_preview: true) }
  scope :ordered, -> { order(:sort_order) }

  # Serialization
  serialize :subtitles, coder: JSON
  serialize :video_metadata, coder: JSON

  # Instance methods
  def duration_minutes
    (duration_seconds / 60.0).ceil
  end

  def duration_formatted
    minutes = duration_seconds / 60
    seconds = duration_seconds % 60
    format('%02d:%02d', minutes, seconds)
  end

  def completed_by?(patient)
    return false unless patient
    progress = video_progresses.find_by(patient: patient)
    progress&.completed? || false
  end

  def progress_for(patient)
    return 0.0 unless patient
    progress = video_progresses.find_by(patient: patient)
    progress&.progress_percentage || 0.0
  end

  def watched_seconds_for(patient)
    return 0 unless patient
    progress = video_progresses.find_by(patient: patient)
    progress&.watched_seconds || 0
  end

  def can_be_watched_by?(patient)
    return true if is_preview?
    return false unless patient

    # Check if patient is enrolled in the course
    course.course_enrollments.active.exists?(patient: patient)
  end

  def mark_as_watched!(patient, watched_seconds)
    return false unless can_be_watched_by?(patient)

    progress = video_progresses.find_or_initialize_by(patient: patient)
    progress.watched_seconds = [watched_seconds, progress.watched_seconds || 0].max
    progress.progress_percentage = (progress.watched_seconds.to_f / duration_seconds * 100).round(2)
    progress.completed = progress.progress_percentage >= 90.0
    progress.last_watched_at = Time.current
    progress.first_watched_at ||= Time.current
    progress.watch_count += 1 if progress.watched_seconds_changed?

    if progress.completed? && !progress.completed_at
      progress.completed_at = Time.current
    end

    progress.save!
    progress
  end

  def increment_view_count!
    increment!(:view_count)
  end

  def has_subtitles?
    subtitles.present? && subtitles.is_a?(Array) && subtitles.any?
  end

  def subtitle_languages
    return [] unless has_subtitles?
    subtitles.map { |sub| sub['language'] }.compact.uniq
  end

  private

  def update_module_duration
    total_seconds = course_module.course_videos.sum(:duration_seconds)
    course_module.update_column(:duration_minutes, (total_seconds / 60.0).ceil)
  end
end
