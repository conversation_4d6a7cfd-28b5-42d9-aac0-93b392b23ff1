class MembershipTier < ApplicationRecord
  # Associations
  has_many :tier_features, dependent: :destroy
  has_many :membership_features, through: :tier_features
  has_many :user_memberships, dependent: :destroy
  has_many :users, through: :user_memberships

  # Enums
  enum billing_cycle: {
    monthly: 1,
    quarterly: 3,
    semi_annually: 6,
    annually: 12
  }

  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :slug, presence: true, uniqueness: true,
            format: { with: /\A[a-z0-9_-]+\z/, message: "must contain only lowercase letters, numbers, hyphens, and underscores" }
  validates :description, presence: true
  validates :price, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :currency, presence: true, inclusion: { in: %w[TRY USD EUR] }
  validates :billing_cycle, presence: true
  validates :trial_days, numericality: { greater_than_or_equal_to: 0 }
  validates :sort_order, presence: true, numericality: { greater_than: 0 }

  # Callbacks
  before_validation :generate_slug, on: :create

  # Scopes
  scope :active, -> { where(active: true) }
  scope :inactive, -> { where(active: false) }
  scope :ordered, -> { order(:sort_order) }
  scope :free, -> { where(price: 0) }
  scope :paid, -> { where('price > 0') }

  # Instance methods
  def free?
    price == 0
  end

  def paid?
    price > 0
  end

  def has_trial?
    trial_days > 0
  end

  def monthly_price
    case billing_cycle
    when 'monthly' then price
    when 'quarterly' then (price / 3.0).round(2)
    when 'semi_annually' then (price / 6.0).round(2)
    when 'annually' then (price / 12.0).round(2)
    else price
    end
  end

  def feature_limit(feature_key)
    tier_feature = tier_features.joins(:membership_feature)
                                .find_by(membership_features: { feature_key: feature_key })
    return nil unless tier_feature

    tier_feature.is_unlimited? ? Float::INFINITY : tier_feature.limit_value
  end

  def has_feature?(feature_key)
    tier_features.joins(:membership_feature)
                 .exists?(membership_features: { feature_key: feature_key }, is_included: true)
  end

  private

  def generate_slug
    return if slug.present?
    self.slug = name.parameterize if name.present?
  end
end
