# AZMed+ Comprehensive Seed File
# This file creates sample data for the healthcare application
# Run with: rails db:seed

puts "🌱 Starting AZMed+ seed process..."

# Clear existing data in development
if Rails.env.development?
  puts "🧹 Cleaning existing data..."

  # Clear in dependency order
  model_names = [
    'VideoProgress', 'CourseEnrollment', 'CourseVideo', 'CourseModule', 'Course',
    'SessionParticipant', 'GroupSession', 'Prescription', 'Consultation', 'Appointment',
    'PregnancyTracker', 'CycleTracker', 'HealthMetric', 'AiHealthInsight', 'RiskAssessment',
    'MedicalDocument', 'Attachment', 'Notification', 'Payment', 'OrderItem', 'Order',
    'PaymentMethod', 'CalendarIntegration', 'UsageTracker', 'UserMembership',
    'TierFeature', 'MembershipFeature', 'MembershipTier', 'DoctorVerification',
    'MedicalLicense', 'DoctorAvailability', 'HealthProfile', 'Patient', 'Doctor', 'Admin', 'User'
  ]

  model_names.each do |model_name|
    begin
      model_class = model_name.constantize
      model_class.delete_all
    rescue NameError
      puts "⚠️  Model #{model_name} not found, skipping..."
    end
  end

  puts "✅ Existing data cleared"
end

# Helper methods
def random_phone
  "+90#{rand(500..599)}#{rand(100..999)}#{rand(1000..9999)}"
end

def random_email(name)
  # Remove special characters and ensure valid email format
  clean_name = name.downcase.gsub(/[^a-z\s]/, '').gsub(' ', '.')
  "#{clean_name}#{rand(100..999)}@example.com"
end

def random_date_between(start_date, end_date)
  Time.at(rand(start_date.to_time.to_i..end_date.to_time.to_i)).to_date
end

def random_datetime_between(start_time, end_time)
  Time.at(rand(start_time.to_i..end_time.to_i))
end

# 1. Create Membership Features
puts "📋 Creating membership features..."

membership_features_data = [
  {
    name: "Video Consultations",
    feature_key: "video_consultations",
    description: "Access to video consultations with doctors",
    feature_type: "countable",
    category: "consultations"
  },
  {
    name: "AI Health Insights",
    feature_key: "ai_health_insights",
    description: "AI-powered health insights and recommendations",
    feature_type: "boolean",
    category: "ai_features"
  },
  {
    name: "Pregnancy Tracking",
    feature_key: "pregnancy_tracking",
    description: "Comprehensive pregnancy tracking and monitoring",
    feature_type: "boolean",
    category: "health_tracking"
  },
  {
    name: "Cycle Tracking",
    feature_key: "cycle_tracking",
    description: "Menstrual cycle tracking and predictions",
    feature_type: "boolean",
    category: "health_tracking"
  },
  {
    name: "Educational Courses",
    feature_key: "educational_courses",
    description: "Access to health education courses",
    feature_type: "countable",
    category: "education"
  },
  {
    name: "Priority Support",
    feature_key: "priority_support",
    description: "Priority customer support",
    feature_type: "boolean",
    category: "support"
  },
  {
    name: "Health Document Storage",
    feature_key: "document_storage",
    description: "Secure storage for health documents",
    feature_type: "countable",
    category: "storage"
  },
  {
    name: "Emergency Consultations",
    feature_key: "emergency_consultations",
    description: "24/7 emergency consultation access",
    feature_type: "boolean",
    category: "consultations"
  }
]

membership_features_data.each do |feature_data|
  MembershipFeature.find_or_create_by!(feature_key: feature_data[:feature_key]) do |feature|
    feature.assign_attributes(feature_data)
  end
end

puts "✅ Created #{MembershipFeature.count} membership features"

# 2. Create Membership Tiers
puts "💎 Creating membership tiers..."

membership_tiers_data = [
  {
    name: "Free",
    slug: "free",
    description: "Basic health tracking and limited consultations",
    price: 0.0,
    currency: "TRY",
    billing_cycle: "monthly",
    trial_days: 0,
    sort_order: 1,
    active: true
  },
  {
    name: "Basic",
    slug: "basic",
    description: "Enhanced health tracking with AI insights",
    price: 49.99,
    currency: "TRY",
    billing_cycle: "monthly",
    trial_days: 7,
    sort_order: 2,
    active: true
  },
  {
    name: "Premium",
    slug: "premium",
    description: "Full access to all health features and unlimited consultations",
    price: 99.99,
    currency: "TRY",
    billing_cycle: "monthly",
    trial_days: 14,
    sort_order: 3,
    active: true
  },
  {
    name: "Family",
    slug: "family",
    description: "Premium features for up to 4 family members",
    price: 179.99,
    currency: "TRY",
    billing_cycle: "monthly",
    trial_days: 14,
    sort_order: 4,
    active: true
  }
]

membership_tiers_data.each do |tier_data|
  MembershipTier.find_or_create_by!(slug: tier_data[:slug]) do |tier|
    tier.assign_attributes(tier_data)
  end
end

puts "✅ Created #{MembershipTier.count} membership tiers"

# 3. Create Tier Features (Feature assignments to tiers)
puts "🔗 Creating tier features..."

tier_features_data = [
  # Free Tier
  { tier_slug: "free", feature_key: "video_consultations", limit_value: 1, is_included: true },
  { tier_slug: "free", feature_key: "cycle_tracking", is_included: true, is_unlimited: true },
  { tier_slug: "free", feature_key: "document_storage", limit_value: 5, is_included: true },

  # Basic Tier
  { tier_slug: "basic", feature_key: "video_consultations", limit_value: 5, is_included: true },
  { tier_slug: "basic", feature_key: "ai_health_insights", is_included: true, is_unlimited: true },
  { tier_slug: "basic", feature_key: "pregnancy_tracking", is_included: true, is_unlimited: true },
  { tier_slug: "basic", feature_key: "cycle_tracking", is_included: true, is_unlimited: true },
  { tier_slug: "basic", feature_key: "educational_courses", limit_value: 3, is_included: true },
  { tier_slug: "basic", feature_key: "document_storage", limit_value: 20, is_included: true },

  # Premium Tier
  { tier_slug: "premium", feature_key: "video_consultations", is_included: true, is_unlimited: true },
  { tier_slug: "premium", feature_key: "ai_health_insights", is_included: true, is_unlimited: true },
  { tier_slug: "premium", feature_key: "pregnancy_tracking", is_included: true, is_unlimited: true },
  { tier_slug: "premium", feature_key: "cycle_tracking", is_included: true, is_unlimited: true },
  { tier_slug: "premium", feature_key: "educational_courses", is_included: true, is_unlimited: true },
  { tier_slug: "premium", feature_key: "priority_support", is_included: true, is_unlimited: true },
  { tier_slug: "premium", feature_key: "document_storage", is_included: true, is_unlimited: true },
  { tier_slug: "premium", feature_key: "emergency_consultations", is_included: true, is_unlimited: true },

  # Family Tier
  { tier_slug: "family", feature_key: "video_consultations", is_included: true, is_unlimited: true },
  { tier_slug: "family", feature_key: "ai_health_insights", is_included: true, is_unlimited: true },
  { tier_slug: "family", feature_key: "pregnancy_tracking", is_included: true, is_unlimited: true },
  { tier_slug: "family", feature_key: "cycle_tracking", is_included: true, is_unlimited: true },
  { tier_slug: "family", feature_key: "educational_courses", is_included: true, is_unlimited: true },
  { tier_slug: "family", feature_key: "priority_support", is_included: true, is_unlimited: true },
  { tier_slug: "family", feature_key: "document_storage", is_included: true, is_unlimited: true },
  { tier_slug: "family", feature_key: "emergency_consultations", is_included: true, is_unlimited: true }
]

tier_features_data.each do |tf_data|
  tier = MembershipTier.find_by!(slug: tf_data[:tier_slug])
  feature = MembershipFeature.find_by!(feature_key: tf_data[:feature_key])

  TierFeature.find_or_create_by!(
    membership_tier: tier,
    membership_feature: feature
  ) do |tier_feature|
    tier_feature.limit_value = tf_data[:limit_value]
    tier_feature.is_unlimited = tf_data[:is_unlimited] || false
    tier_feature.is_included = tf_data[:is_included] || false
  end
end

puts "✅ Created #{TierFeature.count} tier features"

# 4. Create Admin Users
puts "👑 Creating admin users..."

admin_users_data = [
  {
    first_name: "System",
    last_name: "Administrator",
    email: "<EMAIL>",
    phone_number: "+905551234567",
    birth_date: Date.new(1985, 1, 15),
    admin_data: {
      role: "super_admin",
      department: "IT",
      permissions: {
        "users" => true,
        "doctors" => true,
        "content" => true,
        "billing" => true
      }
    }
  },
  {
    first_name: "Medical",
    last_name: "Coordinator",
    email: "<EMAIL>",
    phone_number: "+905551234568",
    birth_date: Date.new(1988, 3, 22),
    admin_data: {
      role: "admin",
      department: "Medical",
      permissions: {
        "users" => true,
        "doctors" => true,
        "content" => false,
        "billing" => false
      }
    }
  }
]

admin_users_data.each do |user_data|
  admin_data = user_data.delete(:admin_data)

  user = User.find_or_create_by!(email: user_data[:email]) do |u|
    u.assign_attributes(user_data)
    u.password = "password123"
    u.verified_at = Time.current
  end

  Admin.find_or_create_by!(user: user) do |admin|
    admin.assign_attributes(admin_data)
  end
end

puts "✅ Created #{Admin.count} admin users"

# 5. Create Doctor Users
puts "👨‍⚕️ Creating doctor users..."

specializations = [
  "Obstetrics and Gynecology", "Internal Medicine", "Pediatrics",
  "Cardiology", "Dermatology", "Psychiatry", "Endocrinology",
  "Family Medicine", "Neurology", "Orthopedics"
]

universities = [
  "Istanbul University", "Hacettepe University", "Ankara University",
  "Ege University", "Gazi University", "Marmara University"
]

doctor_users_data = [
  {
    first_name: "Ayşe",
    last_name: "Demir",
    email: "<EMAIL>",
    specialization: "Obstetrics and Gynecology",
    sub_specializations: ["Maternal-Fetal Medicine", "Reproductive Endocrinology"],
    languages_spoken: ["Turkish", "English"],
    bio: "Experienced OB/GYN with 15 years of practice, specializing in high-risk pregnancies."
  },
  {
    first_name: "Mehmet",
    last_name: "Özkan",
    email: "<EMAIL>",
    specialization: "Internal Medicine",
    sub_specializations: ["Diabetes", "Hypertension"],
    languages_spoken: ["Turkish", "English", "German"],
    bio: "Internal medicine specialist with expertise in chronic disease management."
  },
  {
    first_name: "Fatma",
    last_name: "Yılmaz",
    email: "<EMAIL>",
    specialization: "Pediatrics",
    sub_specializations: ["Neonatology", "Pediatric Cardiology"],
    languages_spoken: ["Turkish", "English"],
    bio: "Pediatrician dedicated to providing comprehensive care for children and adolescents."
  },
  {
    first_name: "Ali",
    last_name: "Kaya",
    email: "<EMAIL>",
    specialization: "Cardiology",
    sub_specializations: ["Interventional Cardiology", "Heart Failure"],
    languages_spoken: ["Turkish", "English", "French"],
    bio: "Cardiologist with extensive experience in interventional procedures."
  },
  {
    first_name: "Zeynep",
    last_name: "Şahin",
    email: "<EMAIL>",
    specialization: "Dermatology",
    sub_specializations: ["Cosmetic Dermatology", "Dermatopathology"],
    languages_spoken: ["Turkish", "English"],
    bio: "Dermatologist specializing in both medical and cosmetic dermatology."
  }
]

doctor_users_data.each_with_index do |doctor_data, index|
  user_data = doctor_data.except(:specialization, :sub_specializations, :languages_spoken, :bio)
  user_data[:phone_number] = random_phone
  user_data[:birth_date] = random_date_between(Date.new(1970, 1, 1), Date.new(1990, 12, 31))

  user = User.find_or_create_by!(email: user_data[:email]) do |u|
    u.assign_attributes(user_data)
    u.password = "password123"
    u.verified_at = Time.current
  end

  Doctor.find_or_create_by!(user: user) do |doctor|
    doctor.license_number = "TR#{rand(100000..999999)}"
    doctor.specialization = doctor_data[:specialization]
    doctor.sub_specializations = doctor_data[:sub_specializations]
    doctor.graduated_university = universities.sample
    doctor.graduation_year = rand(1995..2015)
    doctor.current_institution = "AZMed+ Healthcare"
    doctor.years_experience = Date.current.year - doctor.graduation_year
    doctor.consultation_fee_day = rand(150..300).to_f
    doctor.consultation_fee_night = doctor.consultation_fee_day * 1.5
    doctor.consultation_fee_emergency = doctor.consultation_fee_day * 2.0
    doctor.languages_spoken = doctor_data[:languages_spoken]
    doctor.bio = doctor_data[:bio]
    doctor.verification_status = "verified"
    doctor.rating = rand(4.0..5.0).round(1)
    doctor.total_consultations = rand(50..500)
  end
end

puts "✅ Created #{Doctor.count} doctor users"

# 6. Create Patient Users
puts "👥 Creating patient users..."

patient_names = [
  ["Elif", "Arslan"], ["Burak", "Çelik"], ["Selin", "Doğan"], ["Emre", "Erdoğan"],
  ["Aylin", "Güneş"], ["Cem", "Kılıç"], ["Deniz", "Koç"], ["Gizem", "Özdemir"],
  ["Kerem", "Polat"], ["Merve", "Şen"], ["Oğuz", "Taş"], ["Pınar", "Uysal"],
  ["Serkan", "Yıldız"], ["Tuba", "Acar"], ["Volkan", "Başar"], ["Yeliz", "Çakır"],
  ["Zafer", "Demirci"], ["Aslı", "Ergin"], ["Bora", "Fidan"], ["Canan", "Gül"]
]

blood_types = ["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-"]

patient_names.each_with_index do |(first_name, last_name), index|
  email = random_email("#{first_name} #{last_name}")

  user = User.find_or_create_by!(email: email) do |u|
    u.first_name = first_name
    u.last_name = last_name
    u.phone_number = random_phone
    u.birth_date = random_date_between(Date.new(1980, 1, 1), Date.new(2005, 12, 31))
    u.password = "password123"
    u.verified_at = rand > 0.1 ? Time.current : nil # 90% verified
  end

  Patient.find_or_create_by!(user: user) do |patient|
    patient.emergency_contact_name = ["Anne", "Baba", "Eş", "Kardeş"].sample + " - " +
                                   ["Ayşe Yılmaz", "Mehmet Kaya", "Fatma Demir", "Ali Özkan"].sample
    patient.emergency_contact_phone = random_phone
    patient.blood_type = blood_types.sample if rand > 0.3 # 70% have blood type
    patient.pregnancy_count = user.first_name.in?(["Elif", "Selin", "Aylin", "Gizem", "Merve", "Pınar", "Tuba", "Yeliz", "Aslı", "Canan"]) ? rand(0..3) : 0
    patient.birth_count = patient.pregnancy_count > 0 ? rand(0..patient.pregnancy_count) : 0
    patient.smoking_status = rand(0..2)
    patient.alcohol_consumption = rand(0..2)
  end
end

puts "✅ Created #{Patient.count} patient users"

# 7. Create User Memberships
puts "💳 Creating user memberships..."

# Assign memberships to patients
Patient.includes(:user).each do |patient|
  # 60% get free, 25% basic, 10% premium, 5% family
  tier_weights = { "free" => 60, "basic" => 25, "premium" => 10, "family" => 5 }
  random_value = rand(100)

  selected_tier = if random_value < 60
    "free"
  elsif random_value < 85
    "basic"
  elsif random_value < 95
    "premium"
  else
    "family"
  end

  tier = MembershipTier.find_by!(slug: selected_tier)

  UserMembership.find_or_create_by!(
    user: patient.user,
    membership_tier: tier
  ) do |membership|
    membership.status = "active"
    membership.starts_at = rand(30.days.ago..Time.current)
    membership.expires_at = membership.starts_at + 1.month
    membership.auto_renewal = rand > 0.3 # 70% auto-renewal
  end
end

puts "✅ Created #{UserMembership.count} user memberships"

# 8. Create Doctor Availabilities
puts "📅 Creating doctor availabilities..."

Doctor.all.each do |doctor|
  (0..6).each do |day_of_week| # Sunday = 0, Monday = 1, etc.
    # Most doctors work Monday-Friday, some work weekends
    is_available = if day_of_week.in?([1, 2, 3, 4, 5]) # Mon-Fri
      rand > 0.1 # 90% available
    else # Weekend
      rand > 0.7 # 30% available
    end

    if is_available
      start_time = ["08:00", "09:00", "10:00"].sample
      end_time = ["17:00", "18:00", "19:00"].sample

      DoctorAvailability.find_or_create_by!(
        doctor: doctor,
        day_of_week: day_of_week
      ) do |availability|
        availability.start_time = start_time
        availability.end_time = end_time
        availability.is_available = true
        availability.max_appointments = rand(8..16)
      end
    end
  end
end

puts "✅ Created #{DoctorAvailability.count} doctor availabilities"

# 9. Create Sample Appointments and Consultations
puts "📋 Creating sample appointments and consultations..."

# Create some past and future appointments
Patient.includes(:user).limit(10).each do |patient|
  doctor = Doctor.verified.sample
  next unless doctor

  # Create 1-3 appointments per patient
  rand(1..3).times do
    # Mix of past and future appointments
    if rand > 0.5
      # Past appointment
      scheduled_at = random_datetime_between(30.days.ago, 1.day.ago)
      status = ["completed", "cancelled"].sample
    else
      # Future appointment
      scheduled_at = random_datetime_between(1.day.from_now, 30.days.from_now)
      status = "scheduled"
    end

    appointment = Appointment.new(
      patient: patient,
      doctor: doctor,
      appointment_type: ["consultation", "follow_up", "routine_checkup"].sample,
      scheduled_at: scheduled_at,
      ends_at: scheduled_at + 30.minutes,
      duration_minutes: 30,
      consultation_fee: doctor.consultation_fee_day,
      status: status
    )

    # Skip validations for past appointments in seed data
    if scheduled_at < Time.current
      appointment.skip_future_validation = true
      appointment.skip_availability_validation = true
    end

    appointment.save!

    # Create consultation for completed appointments
    if appointment.completed?
      consultation = Consultation.create!(
        appointment: appointment,
        patient: patient,
        doctor: doctor,
        consultation_type: "initial",
        consultation_method: "video",
        status: "completed",
        started_at: appointment.scheduled_at,
        ended_at: appointment.ends_at,
        duration_minutes: 30,
        chief_complaint: [
          "Headache and fatigue",
          "Abdominal pain",
          "Skin rash",
          "Chest pain",
          "Back pain",
          "Anxiety symptoms"
        ].sample,
        assessment: "Patient presents with typical symptoms. Recommended treatment plan discussed.",
        plan: "Follow-up in 2 weeks. Prescribed medication as needed.",
        consultation_fee: appointment.consultation_fee
      )

      # Some consultations have prescriptions
      if rand > 0.4 # 60% have prescriptions
        Prescription.create!(
          consultation: consultation,
          patient: patient,
          doctor: doctor,
          issued_at: consultation.ended_at,
          expires_at: consultation.ended_at + 30.days,
          diagnosis: consultation.assessment,
          medications: [
            {
              name: ["Paracetamol", "Ibuprofen", "Amoxicillin", "Omeprazole"].sample,
              dosage: "500mg",
              frequency: "2 times daily",
              duration: "7 days",
              instructions: "Take with food"
            }
          ],
          instructions: "Take as prescribed. Contact doctor if symptoms persist.",
          status: "active"
        )
      end
    end
  end
end

puts "✅ Created #{Appointment.count} appointments"
puts "✅ Created #{Consultation.count} consultations"
puts "✅ Created #{Prescription.count} prescriptions"

# 10. Create Sample Health Profiles
puts "🏥 Creating health profiles..."

Patient.limit(15).all.each do |patient|
  HealthProfile.find_or_create_by!(patient: patient) do |profile|
    profile.height = rand(150..190)
    profile.weight = rand(50..100)
    profile.allergies = ["Penicillin", "Peanuts", "Shellfish", "None"].sample
    profile.chronic_conditions = rand > 0.7 ? ["Diabetes", "Hypertension", "Asthma"].sample : nil
    profile.current_medications = rand > 0.6 ? ["Metformin", "Lisinopril", "Albuterol"].sample : nil
    profile.family_history = rand > 0.5 ? ["Heart disease", "Diabetes", "Cancer"].sample : nil
    profile.lifestyle_notes = "Regular exercise, balanced diet"
  end
end

puts "✅ Created #{HealthProfile.count} health profiles"

# Summary
puts "\n🎉 Seed process completed successfully!"
puts "=" * 50
puts "📊 SUMMARY:"
puts "Users: #{User.count} (#{Admin.count} admins, #{Doctor.count} doctors, #{Patient.count} patients)"
puts "Membership Tiers: #{MembershipTier.count}"
puts "Membership Features: #{MembershipFeature.count}"
puts "Tier Features: #{TierFeature.count}"
puts "User Memberships: #{UserMembership.count}"
puts "Doctor Availabilities: #{DoctorAvailability.count}"
puts "Appointments: #{Appointment.count}"
puts "Consultations: #{Consultation.count}"
puts "Prescriptions: #{Prescription.count}"
puts "Health Profiles: #{HealthProfile.count}"
puts "=" * 50

puts "\n🔐 LOGIN CREDENTIALS:"
puts "Admin: <EMAIL> / password123"
puts "Medical Coordinator: <EMAIL> / password123"
puts "Sample Doctor: <EMAIL> / password123"
puts "Sample Patient: Use any patient email / password123"
puts "\n✨ Ready to use AZMed+ Healthcare Platform!"
