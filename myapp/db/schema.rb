# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_06_20_130032) do
  create_table "admins", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "role", default: "admin", null: false
    t.text "permissions"
    t.string "department", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["role"], name: "index_admins_on_role"
    t.index ["user_id"], name: "index_admins_on_user_id"
  end

  create_table "doctors", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "license_number", null: false
    t.string "specialization", null: false
    t.text "sub_specializations"
    t.string "graduated_university", null: false
    t.integer "graduation_year", null: false
    t.string "current_institution"
    t.integer "years_experience", default: 0
    t.decimal "consultation_fee_day", precision: 8, scale: 2, null: false
    t.decimal "consultation_fee_night", precision: 8, scale: 2, null: false
    t.decimal "consultation_fee_emergency", precision: 8, scale: 2, null: false
    t.text "languages_spoken"
    t.text "bio"
    t.integer "verification_status", default: 0
    t.decimal "rating", precision: 3, scale: 2
    t.integer "total_consultations", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["license_number"], name: "index_doctors_on_license_number", unique: true
    t.index ["rating"], name: "index_doctors_on_rating"
    t.index ["specialization"], name: "index_doctors_on_specialization"
    t.index ["user_id"], name: "index_doctors_on_user_id"
    t.index ["verification_status"], name: "index_doctors_on_verification_status"
  end

  create_table "membership_features", force: :cascade do |t|
    t.string "name", null: false
    t.string "feature_key", null: false
    t.text "description", null: false
    t.integer "feature_type", default: 0, null: false
    t.string "category", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category"], name: "index_membership_features_on_category"
    t.index ["feature_key"], name: "index_membership_features_on_feature_key", unique: true
    t.index ["feature_type"], name: "index_membership_features_on_feature_type"
  end

  create_table "membership_tiers", force: :cascade do |t|
    t.string "name", null: false
    t.string "slug", null: false
    t.text "description", null: false
    t.decimal "price", precision: 8, scale: 2, default: "0.0", null: false
    t.string "currency", default: "TRY", null: false
    t.integer "billing_cycle", default: 1, null: false
    t.integer "trial_days", default: 0
    t.integer "sort_order", null: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_membership_tiers_on_active"
    t.index ["price"], name: "index_membership_tiers_on_price"
    t.index ["slug"], name: "index_membership_tiers_on_slug", unique: true
    t.index ["sort_order"], name: "index_membership_tiers_on_sort_order"
  end

  create_table "patients", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "medical_record_number", null: false
    t.string "emergency_contact_name", null: false
    t.string "emergency_contact_phone", null: false
    t.string "blood_type"
    t.integer "pregnancy_count", default: 0
    t.integer "birth_count", default: 0
    t.integer "smoking_status", default: 0
    t.integer "alcohol_consumption", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["blood_type"], name: "index_patients_on_blood_type"
    t.index ["medical_record_number"], name: "index_patients_on_medical_record_number", unique: true
    t.index ["user_id"], name: "index_patients_on_user_id"
  end

  create_table "tier_features", force: :cascade do |t|
    t.integer "membership_tier_id", null: false
    t.integer "membership_feature_id", null: false
    t.integer "limit_value"
    t.boolean "is_unlimited", default: false
    t.boolean "is_included", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["is_included"], name: "index_tier_features_on_is_included"
    t.index ["is_unlimited"], name: "index_tier_features_on_is_unlimited"
    t.index ["membership_feature_id"], name: "index_tier_features_on_membership_feature_id"
    t.index ["membership_tier_id", "membership_feature_id"], name: "index_tier_features_on_tier_and_feature", unique: true
    t.index ["membership_tier_id"], name: "index_tier_features_on_membership_tier_id"
  end

  create_table "usage_trackers", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "membership_feature_id", null: false
    t.date "period_start", null: false
    t.date "period_end", null: false
    t.integer "usage_count", default: 0
    t.boolean "limit_exceeded", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["limit_exceeded"], name: "index_usage_trackers_on_limit_exceeded"
    t.index ["membership_feature_id"], name: "index_usage_trackers_on_membership_feature_id"
    t.index ["period_start"], name: "index_usage_trackers_on_period_start"
    t.index ["user_id", "membership_feature_id", "period_start"], name: "index_usage_trackers_unique", unique: true
    t.index ["user_id"], name: "index_usage_trackers_on_user_id"
  end

  create_table "user_memberships", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "membership_tier_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "starts_at", null: false
    t.datetime "expires_at", null: false
    t.boolean "auto_renewal", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["expires_at"], name: "index_user_memberships_on_expires_at"
    t.index ["membership_tier_id"], name: "index_user_memberships_on_membership_tier_id"
    t.index ["starts_at"], name: "index_user_memberships_on_starts_at"
    t.index ["status"], name: "index_user_memberships_on_status"
    t.index ["user_id", "status"], name: "index_user_memberships_on_user_id_and_status"
    t.index ["user_id"], name: "index_user_memberships_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", null: false
    t.string "password_digest", null: false
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.string "phone_number", null: false
    t.date "birth_date", null: false
    t.datetime "verified_at"
    t.string "locale", default: "tr"
    t.string "timezone", default: "Europe/Istanbul"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["phone_number"], name: "index_users_on_phone_number"
    t.index ["verified_at"], name: "index_users_on_verified_at"
  end

  add_foreign_key "admins", "users"
  add_foreign_key "doctors", "users"
  add_foreign_key "patients", "users"
  add_foreign_key "tier_features", "membership_features"
  add_foreign_key "tier_features", "membership_tiers"
  add_foreign_key "usage_trackers", "membership_features"
  add_foreign_key "usage_trackers", "users"
  add_foreign_key "user_memberships", "membership_tiers"
  add_foreign_key "user_memberships", "users"
end
