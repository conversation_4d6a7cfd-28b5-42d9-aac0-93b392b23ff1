# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_06_20_132917) do
  create_table "admins", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "role", default: "admin", null: false
    t.text "permissions"
    t.string "department", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["role"], name: "index_admins_on_role"
    t.index ["user_id"], name: "index_admins_on_user_id"
  end

  create_table "ai_health_insights", force: :cascade do |t|
    t.integer "patient_id", null: false
    t.string "insight_type", null: false
    t.string "title", null: false
    t.text "content", null: false
    t.text "summary"
    t.decimal "confidence_score", precision: 5, scale: 4, null: false
    t.datetime "generated_at", null: false
    t.integer "status", default: 0, null: false
    t.integer "priority", default: 1
    t.json "data_sources"
    t.json "recommendations"
    t.json "metadata"
    t.string "ai_model_version"
    t.boolean "reviewed_by_doctor", default: false
    t.integer "reviewed_by_id"
    t.datetime "reviewed_at"
    t.text "doctor_notes"
    t.boolean "patient_acknowledged", default: false
    t.datetime "acknowledged_at"
    t.datetime "expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["confidence_score"], name: "index_ai_health_insights_on_confidence_score"
    t.index ["generated_at"], name: "index_ai_health_insights_on_generated_at"
    t.index ["insight_type"], name: "index_ai_health_insights_on_insight_type"
    t.index ["patient_id", "generated_at"], name: "index_ai_health_insights_on_patient_id_and_generated_at"
    t.index ["patient_id"], name: "index_ai_health_insights_on_patient_id"
    t.index ["priority"], name: "index_ai_health_insights_on_priority"
    t.index ["reviewed_by_doctor"], name: "index_ai_health_insights_on_reviewed_by_doctor"
    t.index ["reviewed_by_id"], name: "index_ai_health_insights_on_reviewed_by_id"
    t.index ["status"], name: "index_ai_health_insights_on_status"
  end

  create_table "appointments", force: :cascade do |t|
    t.integer "patient_id", null: false
    t.integer "doctor_id", null: false
    t.string "appointment_type", null: false
    t.datetime "scheduled_at", null: false
    t.datetime "ends_at", null: false
    t.integer "status", default: 0, null: false
    t.integer "duration_minutes", default: 30, null: false
    t.decimal "consultation_fee", precision: 8, scale: 2, null: false
    t.text "patient_notes"
    t.text "doctor_notes"
    t.string "meeting_link"
    t.string "meeting_id"
    t.datetime "started_at"
    t.datetime "ended_at"
    t.string "cancellation_reason"
    t.datetime "cancelled_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["appointment_type"], name: "index_appointments_on_appointment_type"
    t.index ["doctor_id", "scheduled_at"], name: "index_appointments_on_doctor_id_and_scheduled_at"
    t.index ["doctor_id"], name: "index_appointments_on_doctor_id"
    t.index ["patient_id", "scheduled_at"], name: "index_appointments_on_patient_id_and_scheduled_at"
    t.index ["patient_id"], name: "index_appointments_on_patient_id"
    t.index ["scheduled_at"], name: "index_appointments_on_scheduled_at"
    t.index ["status"], name: "index_appointments_on_status"
  end

  create_table "attachments", force: :cascade do |t|
    t.string "attachable_type", null: false
    t.integer "attachable_id", null: false
    t.integer "user_id", null: false
    t.string "file_name", null: false
    t.string "original_file_name", null: false
    t.string "file_url", null: false
    t.string "file_type", null: false
    t.bigint "file_size_bytes", null: false
    t.string "file_hash"
    t.string "mime_type"
    t.text "description"
    t.boolean "is_public", default: false
    t.boolean "is_processed", default: false
    t.json "processing_metadata"
    t.string "thumbnail_url"
    t.datetime "uploaded_at", null: false
    t.datetime "last_accessed_at"
    t.integer "download_count", default: 0
    t.datetime "expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["attachable_type", "attachable_id"], name: "index_attachments_on_attachable"
    t.index ["attachable_type", "attachable_id"], name: "index_attachments_on_attachable_type_and_attachable_id"
    t.index ["file_hash"], name: "index_attachments_on_file_hash", unique: true
    t.index ["file_type"], name: "index_attachments_on_file_type"
    t.index ["is_public"], name: "index_attachments_on_is_public"
    t.index ["uploaded_at"], name: "index_attachments_on_uploaded_at"
    t.index ["user_id"], name: "index_attachments_on_user_id"
  end

  create_table "calendar_integrations", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "provider", null: false
    t.string "provider_user_id"
    t.string "access_token", null: false
    t.string "refresh_token"
    t.string "calendar_id"
    t.string "calendar_name"
    t.datetime "token_expires_at"
    t.boolean "active", default: true
    t.boolean "sync_appointments", default: true
    t.boolean "sync_reminders", default: true
    t.datetime "last_sync_at"
    t.json "sync_settings"
    t.json "provider_metadata"
    t.text "sync_errors"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_calendar_integrations_on_active"
    t.index ["last_sync_at"], name: "index_calendar_integrations_on_last_sync_at"
    t.index ["provider"], name: "index_calendar_integrations_on_provider"
    t.index ["user_id", "provider"], name: "index_calendar_integrations_on_user_id_and_provider", unique: true
    t.index ["user_id"], name: "index_calendar_integrations_on_user_id"
  end

  create_table "consultations", force: :cascade do |t|
    t.integer "appointment_id"
    t.integer "patient_id", null: false
    t.integer "doctor_id", null: false
    t.string "consultation_type", null: false
    t.integer "status", default: 0, null: false
    t.datetime "started_at"
    t.datetime "ended_at"
    t.integer "duration_minutes"
    t.text "chief_complaint", null: false
    t.text "history_of_present_illness"
    t.text "physical_examination"
    t.text "assessment"
    t.text "plan"
    t.text "follow_up_instructions"
    t.text "additional_notes"
    t.decimal "consultation_fee", precision: 8, scale: 2
    t.string "consultation_method"
    t.json "vital_signs"
    t.json "symptoms"
    t.boolean "prescription_issued", default: false
    t.datetime "next_follow_up_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["appointment_id"], name: "index_consultations_on_appointment_id"
    t.index ["consultation_type"], name: "index_consultations_on_consultation_type"
    t.index ["doctor_id", "started_at"], name: "index_consultations_on_doctor_id_and_started_at"
    t.index ["doctor_id"], name: "index_consultations_on_doctor_id"
    t.index ["next_follow_up_date"], name: "index_consultations_on_next_follow_up_date"
    t.index ["patient_id", "started_at"], name: "index_consultations_on_patient_id_and_started_at"
    t.index ["patient_id"], name: "index_consultations_on_patient_id"
    t.index ["started_at"], name: "index_consultations_on_started_at"
    t.index ["status"], name: "index_consultations_on_status"
  end

  create_table "course_enrollments", force: :cascade do |t|
    t.integer "course_id", null: false
    t.integer "patient_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "enrolled_at", null: false
    t.datetime "completed_at"
    t.datetime "last_accessed_at"
    t.decimal "progress_percentage", precision: 5, scale: 2, default: "0.0"
    t.integer "total_watch_time_seconds", default: 0
    t.decimal "rating", precision: 3, scale: 2
    t.text "review"
    t.datetime "review_submitted_at"
    t.boolean "certificate_issued", default: false
    t.datetime "certificate_issued_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["completed_at"], name: "index_course_enrollments_on_completed_at"
    t.index ["course_id", "patient_id"], name: "index_course_enrollments_on_course_id_and_patient_id", unique: true
    t.index ["course_id"], name: "index_course_enrollments_on_course_id"
    t.index ["enrolled_at"], name: "index_course_enrollments_on_enrolled_at"
    t.index ["patient_id"], name: "index_course_enrollments_on_patient_id"
    t.index ["progress_percentage"], name: "index_course_enrollments_on_progress_percentage"
    t.index ["status"], name: "index_course_enrollments_on_status"
  end

  create_table "course_modules", force: :cascade do |t|
    t.integer "course_id", null: false
    t.string "title", null: false
    t.text "description"
    t.integer "sort_order", null: false
    t.integer "duration_minutes", default: 0
    t.boolean "is_published", default: false
    t.json "learning_objectives"
    t.text "content_summary"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["course_id", "sort_order"], name: "index_course_modules_on_course_id_and_sort_order", unique: true
    t.index ["course_id"], name: "index_course_modules_on_course_id"
    t.index ["is_published"], name: "index_course_modules_on_is_published"
  end

  create_table "course_videos", force: :cascade do |t|
    t.integer "course_module_id", null: false
    t.string "title", null: false
    t.text "description"
    t.string "video_url", null: false
    t.string "thumbnail_url"
    t.integer "duration_seconds", null: false
    t.integer "sort_order", null: false
    t.boolean "is_published", default: false
    t.boolean "is_preview", default: false
    t.string "video_quality"
    t.bigint "file_size_bytes"
    t.json "subtitles"
    t.json "video_metadata"
    t.integer "view_count", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["course_module_id", "sort_order"], name: "index_course_videos_on_course_module_id_and_sort_order", unique: true
    t.index ["course_module_id"], name: "index_course_videos_on_course_module_id"
    t.index ["is_preview"], name: "index_course_videos_on_is_preview"
    t.index ["is_published"], name: "index_course_videos_on_is_published"
  end

  create_table "courses", force: :cascade do |t|
    t.integer "instructor_id", null: false
    t.string "title", null: false
    t.text "description", null: false
    t.string "slug", null: false
    t.string "category", null: false
    t.string "difficulty_level", null: false
    t.integer "duration_minutes", default: 0
    t.decimal "price", precision: 8, scale: 2, default: "0.0"
    t.string "currency", default: "TRY"
    t.integer "status", default: 0, null: false
    t.datetime "published_at"
    t.string "thumbnail_url"
    t.string "preview_video_url"
    t.json "learning_objectives"
    t.json "prerequisites"
    t.json "target_audience"
    t.integer "enrollment_count", default: 0
    t.decimal "rating", precision: 3, scale: 2
    t.integer "review_count", default: 0
    t.boolean "is_featured", default: false
    t.integer "sort_order", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category"], name: "index_courses_on_category"
    t.index ["difficulty_level"], name: "index_courses_on_difficulty_level"
    t.index ["instructor_id", "status"], name: "index_courses_on_instructor_id_and_status"
    t.index ["instructor_id"], name: "index_courses_on_instructor_id"
    t.index ["is_featured"], name: "index_courses_on_is_featured"
    t.index ["published_at"], name: "index_courses_on_published_at"
    t.index ["rating"], name: "index_courses_on_rating"
    t.index ["slug"], name: "index_courses_on_slug", unique: true
    t.index ["status"], name: "index_courses_on_status"
  end

  create_table "cycle_trackers", force: :cascade do |t|
    t.integer "patient_id", null: false
    t.date "cycle_start_date", null: false
    t.date "cycle_end_date"
    t.date "predicted_next_cycle"
    t.integer "cycle_length"
    t.integer "period_length"
    t.string "flow_intensity"
    t.json "symptoms"
    t.json "mood_data"
    t.text "notes"
    t.boolean "is_irregular", default: false
    t.decimal "basal_body_temperature", precision: 4, scale: 2
    t.string "cervical_mucus_type"
    t.boolean "ovulation_detected", default: false
    t.date "ovulation_date"
    t.json "fertility_signs"
    t.boolean "contraception_used", default: false
    t.string "contraception_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["cycle_start_date"], name: "index_cycle_trackers_on_cycle_start_date"
    t.index ["ovulation_date"], name: "index_cycle_trackers_on_ovulation_date"
    t.index ["patient_id", "cycle_start_date"], name: "index_cycle_trackers_on_patient_id_and_cycle_start_date"
    t.index ["patient_id"], name: "index_cycle_trackers_on_patient_id"
    t.index ["predicted_next_cycle"], name: "index_cycle_trackers_on_predicted_next_cycle"
  end

  create_table "doctor_availabilities", force: :cascade do |t|
    t.integer "doctor_id", null: false
    t.integer "day_of_week", null: false
    t.string "start_time", null: false
    t.string "end_time", null: false
    t.integer "appointment_duration", default: 30, null: false
    t.integer "buffer_time", default: 5
    t.integer "max_appointments", default: 16, null: false
    t.boolean "is_available", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["day_of_week"], name: "index_doctor_availabilities_on_day_of_week"
    t.index ["doctor_id", "day_of_week"], name: "index_doctor_availabilities_on_doctor_id_and_day_of_week", unique: true
    t.index ["doctor_id"], name: "index_doctor_availabilities_on_doctor_id"
    t.index ["is_available"], name: "index_doctor_availabilities_on_is_available"
  end

  create_table "doctor_verifications", force: :cascade do |t|
    t.integer "doctor_id", null: false
    t.string "verification_type"
    t.integer "status"
    t.datetime "submitted_at"
    t.datetime "reviewed_at"
    t.text "reviewer_notes"
    t.text "documents"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["doctor_id"], name: "index_doctor_verifications_on_doctor_id"
  end

  create_table "doctors", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "license_number", null: false
    t.string "specialization", null: false
    t.text "sub_specializations"
    t.string "graduated_university", null: false
    t.integer "graduation_year", null: false
    t.string "current_institution"
    t.integer "years_experience", default: 0
    t.decimal "consultation_fee_day", precision: 8, scale: 2, null: false
    t.decimal "consultation_fee_night", precision: 8, scale: 2, null: false
    t.decimal "consultation_fee_emergency", precision: 8, scale: 2, null: false
    t.text "languages_spoken"
    t.text "bio"
    t.integer "verification_status", default: 0
    t.decimal "rating", precision: 3, scale: 2
    t.integer "total_consultations", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["license_number"], name: "index_doctors_on_license_number", unique: true
    t.index ["rating"], name: "index_doctors_on_rating"
    t.index ["specialization"], name: "index_doctors_on_specialization"
    t.index ["user_id"], name: "index_doctors_on_user_id"
    t.index ["verification_status"], name: "index_doctors_on_verification_status"
  end

  create_table "group_sessions", force: :cascade do |t|
    t.integer "host_id", null: false
    t.string "title", null: false
    t.text "description", null: false
    t.string "session_type", null: false
    t.datetime "scheduled_at", null: false
    t.datetime "ends_at", null: false
    t.integer "duration_minutes", null: false
    t.integer "max_participants", default: 10
    t.integer "current_participants", default: 0
    t.integer "status", default: 0, null: false
    t.decimal "price", precision: 8, scale: 2, default: "0.0"
    t.string "currency", default: "TRY"
    t.string "meeting_link"
    t.string "meeting_id"
    t.string "meeting_password"
    t.datetime "started_at"
    t.datetime "ended_at"
    t.text "session_notes"
    t.json "materials"
    t.boolean "recording_enabled", default: false
    t.string "recording_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["host_id", "scheduled_at"], name: "index_group_sessions_on_host_id_and_scheduled_at"
    t.index ["host_id"], name: "index_group_sessions_on_host_id"
    t.index ["scheduled_at"], name: "index_group_sessions_on_scheduled_at"
    t.index ["session_type"], name: "index_group_sessions_on_session_type"
    t.index ["status"], name: "index_group_sessions_on_status"
  end

  create_table "health_metrics", force: :cascade do |t|
    t.integer "patient_id", null: false
    t.string "trackable_type"
    t.integer "trackable_id"
    t.string "metric_type", null: false
    t.decimal "value", precision: 10, scale: 4, null: false
    t.string "unit", null: false
    t.datetime "recorded_at", null: false
    t.string "source", null: false
    t.text "notes"
    t.json "metadata"
    t.string "device_id"
    t.decimal "reference_min", precision: 10, scale: 4
    t.decimal "reference_max", precision: 10, scale: 4
    t.boolean "is_abnormal", default: false
    t.string "trend"
    t.integer "quality_score"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["is_abnormal"], name: "index_health_metrics_on_is_abnormal"
    t.index ["metric_type"], name: "index_health_metrics_on_metric_type"
    t.index ["patient_id", "metric_type", "recorded_at"], name: "idx_on_patient_id_metric_type_recorded_at_d2a57a400b"
    t.index ["patient_id"], name: "index_health_metrics_on_patient_id"
    t.index ["recorded_at"], name: "index_health_metrics_on_recorded_at"
    t.index ["source"], name: "index_health_metrics_on_source"
    t.index ["trackable_type", "trackable_id"], name: "index_health_metrics_on_trackable"
    t.index ["trackable_type", "trackable_id"], name: "index_health_metrics_on_trackable_type_and_trackable_id"
  end

  create_table "health_profiles", force: :cascade do |t|
    t.integer "patient_id", null: false
    t.decimal "height"
    t.decimal "weight"
    t.text "allergies"
    t.text "chronic_conditions"
    t.text "medications"
    t.text "family_history"
    t.text "lifestyle_notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["patient_id"], name: "index_health_profiles_on_patient_id"
  end

  create_table "medical_documents", force: :cascade do |t|
    t.integer "patient_id", null: false
    t.integer "issued_by_id"
    t.integer "consultation_id"
    t.string "document_type", null: false
    t.string "title", null: false
    t.text "description"
    t.string "file_url", null: false
    t.string "file_name"
    t.string "file_type"
    t.bigint "file_size_bytes"
    t.string "file_hash"
    t.datetime "issued_at", null: false
    t.datetime "expires_at"
    t.integer "status", default: 0, null: false
    t.boolean "is_sensitive", default: false
    t.json "access_permissions"
    t.json "metadata"
    t.string "external_id"
    t.string "institution_name"
    t.datetime "last_accessed_at"
    t.integer "access_count", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["consultation_id"], name: "index_medical_documents_on_consultation_id"
    t.index ["document_type"], name: "index_medical_documents_on_document_type"
    t.index ["expires_at"], name: "index_medical_documents_on_expires_at"
    t.index ["file_hash"], name: "index_medical_documents_on_file_hash", unique: true
    t.index ["is_sensitive"], name: "index_medical_documents_on_is_sensitive"
    t.index ["issued_at"], name: "index_medical_documents_on_issued_at"
    t.index ["issued_by_id"], name: "index_medical_documents_on_issued_by_id"
    t.index ["patient_id", "document_type"], name: "index_medical_documents_on_patient_id_and_document_type"
    t.index ["patient_id"], name: "index_medical_documents_on_patient_id"
    t.index ["status"], name: "index_medical_documents_on_status"
  end

  create_table "medical_licenses", force: :cascade do |t|
    t.integer "doctor_id", null: false
    t.string "license_number", null: false
    t.string "issuing_authority", null: false
    t.date "issue_date", null: false
    t.date "expiry_date", null: false
    t.string "license_type", null: false
    t.integer "status", default: 0
    t.string "document_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["doctor_id"], name: "index_medical_licenses_on_doctor_id"
    t.index ["expiry_date"], name: "index_medical_licenses_on_expiry_date"
    t.index ["license_number"], name: "index_medical_licenses_on_license_number", unique: true
    t.index ["license_type"], name: "index_medical_licenses_on_license_type"
    t.index ["status"], name: "index_medical_licenses_on_status"
  end

  create_table "membership_features", force: :cascade do |t|
    t.string "name", null: false
    t.string "feature_key", null: false
    t.text "description", null: false
    t.integer "feature_type", default: 0, null: false
    t.string "category", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category"], name: "index_membership_features_on_category"
    t.index ["feature_key"], name: "index_membership_features_on_feature_key", unique: true
    t.index ["feature_type"], name: "index_membership_features_on_feature_type"
  end

  create_table "membership_tiers", force: :cascade do |t|
    t.string "name", null: false
    t.string "slug", null: false
    t.text "description", null: false
    t.decimal "price", precision: 8, scale: 2, default: "0.0", null: false
    t.string "currency", default: "TRY", null: false
    t.integer "billing_cycle", default: 1, null: false
    t.integer "trial_days", default: 0
    t.integer "sort_order", null: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_membership_tiers_on_active"
    t.index ["price"], name: "index_membership_tiers_on_price"
    t.index ["slug"], name: "index_membership_tiers_on_slug", unique: true
    t.index ["sort_order"], name: "index_membership_tiers_on_sort_order"
  end

  create_table "notifications", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "notifiable_type"
    t.integer "notifiable_id"
    t.string "notification_type", null: false
    t.string "title", null: false
    t.text "message", null: false
    t.text "action_text"
    t.string "action_url"
    t.datetime "read_at"
    t.datetime "sent_at", null: false
    t.integer "priority", default: 1, null: false
    t.string "delivery_method"
    t.boolean "delivered", default: false
    t.datetime "delivered_at"
    t.json "delivery_metadata"
    t.datetime "expires_at"
    t.string "icon"
    t.string "color"
    t.json "data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["delivered"], name: "index_notifications_on_delivered"
    t.index ["expires_at"], name: "index_notifications_on_expires_at"
    t.index ["notifiable_type", "notifiable_id"], name: "index_notifications_on_notifiable"
    t.index ["notifiable_type", "notifiable_id"], name: "index_notifications_on_notifiable_type_and_notifiable_id"
    t.index ["notification_type"], name: "index_notifications_on_notification_type"
    t.index ["priority"], name: "index_notifications_on_priority"
    t.index ["read_at"], name: "index_notifications_on_read_at"
    t.index ["sent_at"], name: "index_notifications_on_sent_at"
    t.index ["user_id", "read_at"], name: "index_notifications_on_user_id_and_read_at"
    t.index ["user_id"], name: "index_notifications_on_user_id"
  end

  create_table "order_items", force: :cascade do |t|
    t.integer "order_id", null: false
    t.string "orderable_type", null: false
    t.integer "orderable_id", null: false
    t.integer "quantity", default: 1, null: false
    t.decimal "unit_price", precision: 10, scale: 2, null: false
    t.decimal "total_price", precision: 10, scale: 2, null: false
    t.string "item_name", null: false
    t.text "item_description"
    t.json "item_metadata"
    t.decimal "discount_amount", precision: 10, scale: 2, default: "0.0"
    t.string "discount_type"
    t.boolean "is_refundable", default: true
    t.datetime "delivered_at"
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["order_id"], name: "index_order_items_on_order_id"
    t.index ["orderable_type", "orderable_id"], name: "index_order_items_on_orderable"
    t.index ["orderable_type", "orderable_id"], name: "index_order_items_on_orderable_type_and_orderable_id"
    t.index ["status"], name: "index_order_items_on_status"
  end

  create_table "orders", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "order_number", null: false
    t.integer "status", default: 0, null: false
    t.decimal "subtotal_amount", precision: 10, scale: 2, null: false
    t.decimal "tax_amount", precision: 10, scale: 2, default: "0.0"
    t.decimal "discount_amount", precision: 10, scale: 2, default: "0.0"
    t.decimal "total_amount", precision: 10, scale: 2, null: false
    t.string "currency", default: "TRY", null: false
    t.string "order_type"
    t.json "billing_address"
    t.string "discount_code"
    t.text "notes"
    t.datetime "confirmed_at"
    t.datetime "shipped_at"
    t.datetime "delivered_at"
    t.datetime "cancelled_at"
    t.string "cancellation_reason"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["confirmed_at"], name: "index_orders_on_confirmed_at"
    t.index ["order_number"], name: "index_orders_on_order_number", unique: true
    t.index ["order_type"], name: "index_orders_on_order_type"
    t.index ["status"], name: "index_orders_on_status"
    t.index ["user_id", "status"], name: "index_orders_on_user_id_and_status"
    t.index ["user_id"], name: "index_orders_on_user_id"
  end

  create_table "patients", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "medical_record_number", null: false
    t.string "emergency_contact_name", null: false
    t.string "emergency_contact_phone", null: false
    t.string "blood_type"
    t.integer "pregnancy_count", default: 0
    t.integer "birth_count", default: 0
    t.integer "smoking_status", default: 0
    t.integer "alcohol_consumption", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["blood_type"], name: "index_patients_on_blood_type"
    t.index ["medical_record_number"], name: "index_patients_on_medical_record_number", unique: true
    t.index ["user_id"], name: "index_patients_on_user_id"
  end

  create_table "payment_methods", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "payment_type", null: false
    t.string "provider", null: false
    t.string "token", null: false
    t.string "last_four", null: false
    t.string "cardholder_name"
    t.date "expires_at"
    t.string "billing_address_line1"
    t.string "billing_address_line2"
    t.string "billing_city"
    t.string "billing_state"
    t.string "billing_postal_code"
    t.string "billing_country", default: "TR"
    t.boolean "is_default", default: false
    t.boolean "active", default: true
    t.datetime "verified_at"
    t.json "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_payment_methods_on_active"
    t.index ["is_default"], name: "index_payment_methods_on_is_default"
    t.index ["payment_type"], name: "index_payment_methods_on_payment_type"
    t.index ["provider"], name: "index_payment_methods_on_provider"
    t.index ["user_id", "is_default"], name: "index_payment_methods_on_user_id_and_is_default"
    t.index ["user_id"], name: "index_payment_methods_on_user_id"
  end

  create_table "payments", force: :cascade do |t|
    t.integer "order_id", null: false
    t.integer "payment_method_id", null: false
    t.decimal "amount", precision: 10, scale: 2, null: false
    t.string "currency", default: "TRY", null: false
    t.integer "status", default: 0, null: false
    t.string "transaction_id"
    t.string "gateway_transaction_id"
    t.string "payment_gateway"
    t.datetime "processed_at"
    t.datetime "failed_at"
    t.datetime "refunded_at"
    t.decimal "refunded_amount", precision: 10, scale: 2, default: "0.0"
    t.text "failure_reason"
    t.text "gateway_response"
    t.json "gateway_metadata"
    t.string "receipt_url"
    t.boolean "is_test", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["gateway_transaction_id"], name: "index_payments_on_gateway_transaction_id"
    t.index ["order_id", "status"], name: "index_payments_on_order_id_and_status"
    t.index ["order_id"], name: "index_payments_on_order_id"
    t.index ["payment_gateway"], name: "index_payments_on_payment_gateway"
    t.index ["payment_method_id"], name: "index_payments_on_payment_method_id"
    t.index ["processed_at"], name: "index_payments_on_processed_at"
    t.index ["status"], name: "index_payments_on_status"
    t.index ["transaction_id"], name: "index_payments_on_transaction_id", unique: true
  end

  create_table "pregnancy_trackers", force: :cascade do |t|
    t.integer "patient_id", null: false
    t.date "conception_date", null: false
    t.date "due_date", null: false
    t.date "last_menstrual_period"
    t.integer "current_week", default: 0
    t.integer "current_trimester", default: 1
    t.decimal "pre_pregnancy_weight", precision: 5, scale: 2
    t.decimal "current_weight", precision: 5, scale: 2
    t.decimal "weight_gain", precision: 5, scale: 2, default: "0.0"
    t.decimal "fundal_height", precision: 4, scale: 1
    t.integer "fetal_heart_rate"
    t.integer "blood_pressure_systolic"
    t.integer "blood_pressure_diastolic"
    t.decimal "glucose_level", precision: 5, scale: 2
    t.decimal "iron_level", precision: 5, scale: 2
    t.json "symptoms"
    t.json "risk_factors"
    t.text "notes"
    t.boolean "active", default: true
    t.string "pregnancy_type", default: "singleton"
    t.datetime "delivery_date"
    t.string "delivery_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_pregnancy_trackers_on_active"
    t.index ["current_trimester"], name: "index_pregnancy_trackers_on_current_trimester"
    t.index ["current_week"], name: "index_pregnancy_trackers_on_current_week"
    t.index ["due_date"], name: "index_pregnancy_trackers_on_due_date"
    t.index ["patient_id", "active"], name: "index_pregnancy_trackers_on_patient_id_and_active"
    t.index ["patient_id"], name: "index_pregnancy_trackers_on_patient_id"
  end

  create_table "prescriptions", force: :cascade do |t|
    t.integer "consultation_id", null: false
    t.integer "patient_id", null: false
    t.integer "doctor_id", null: false
    t.string "prescription_number", null: false
    t.integer "status", default: 0, null: false
    t.datetime "issued_at", null: false
    t.datetime "expires_at", null: false
    t.text "instructions"
    t.text "diagnosis"
    t.text "additional_notes"
    t.json "medications"
    t.boolean "is_digital", default: true
    t.string "pharmacy_name"
    t.string "pharmacy_address"
    t.datetime "dispensed_at"
    t.string "dispensed_by"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["consultation_id"], name: "index_prescriptions_on_consultation_id"
    t.index ["doctor_id", "issued_at"], name: "index_prescriptions_on_doctor_id_and_issued_at"
    t.index ["doctor_id"], name: "index_prescriptions_on_doctor_id"
    t.index ["expires_at"], name: "index_prescriptions_on_expires_at"
    t.index ["issued_at"], name: "index_prescriptions_on_issued_at"
    t.index ["patient_id", "issued_at"], name: "index_prescriptions_on_patient_id_and_issued_at"
    t.index ["patient_id"], name: "index_prescriptions_on_patient_id"
    t.index ["prescription_number"], name: "index_prescriptions_on_prescription_number", unique: true
    t.index ["status"], name: "index_prescriptions_on_status"
  end

  create_table "risk_assessments", force: :cascade do |t|
    t.integer "patient_id", null: false
    t.integer "assessed_by_id"
    t.string "assessment_type", null: false
    t.string "risk_level", null: false
    t.decimal "risk_score", precision: 5, scale: 2, null: false
    t.json "risk_factors"
    t.json "protective_factors"
    t.text "clinical_notes"
    t.json "recommendations"
    t.datetime "assessed_at", null: false
    t.datetime "valid_until"
    t.boolean "requires_follow_up", default: false
    t.datetime "next_assessment_due"
    t.string "assessment_method"
    t.json "assessment_data"
    t.integer "status", default: 0
    t.integer "superseded_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assessed_at"], name: "index_risk_assessments_on_assessed_at"
    t.index ["assessed_by_id"], name: "index_risk_assessments_on_assessed_by_id"
    t.index ["assessment_type"], name: "index_risk_assessments_on_assessment_type"
    t.index ["next_assessment_due"], name: "index_risk_assessments_on_next_assessment_due"
    t.index ["patient_id", "assessment_type", "assessed_at"], name: "idx_on_patient_id_assessment_type_assessed_at_c1a3b8d791"
    t.index ["patient_id"], name: "index_risk_assessments_on_patient_id"
    t.index ["requires_follow_up"], name: "index_risk_assessments_on_requires_follow_up"
    t.index ["risk_level"], name: "index_risk_assessments_on_risk_level"
    t.index ["superseded_by_id"], name: "index_risk_assessments_on_superseded_by_id"
    t.index ["valid_until"], name: "index_risk_assessments_on_valid_until"
  end

  create_table "session_participants", force: :cascade do |t|
    t.integer "group_session_id", null: false
    t.integer "user_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "registered_at", null: false
    t.datetime "joined_at"
    t.datetime "left_at"
    t.integer "total_duration_seconds", default: 0
    t.boolean "attended", default: false
    t.decimal "attendance_percentage", precision: 5, scale: 2, default: "0.0"
    t.text "feedback"
    t.decimal "rating", precision: 3, scale: 2
    t.datetime "feedback_submitted_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["attended"], name: "index_session_participants_on_attended"
    t.index ["group_session_id", "user_id"], name: "index_session_participants_on_group_session_id_and_user_id", unique: true
    t.index ["group_session_id"], name: "index_session_participants_on_group_session_id"
    t.index ["registered_at"], name: "index_session_participants_on_registered_at"
    t.index ["status"], name: "index_session_participants_on_status"
    t.index ["user_id"], name: "index_session_participants_on_user_id"
  end

  create_table "tier_features", force: :cascade do |t|
    t.integer "membership_tier_id", null: false
    t.integer "membership_feature_id", null: false
    t.integer "limit_value"
    t.boolean "is_unlimited", default: false
    t.boolean "is_included", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["is_included"], name: "index_tier_features_on_is_included"
    t.index ["is_unlimited"], name: "index_tier_features_on_is_unlimited"
    t.index ["membership_feature_id"], name: "index_tier_features_on_membership_feature_id"
    t.index ["membership_tier_id", "membership_feature_id"], name: "index_tier_features_on_tier_and_feature", unique: true
    t.index ["membership_tier_id"], name: "index_tier_features_on_membership_tier_id"
  end

  create_table "usage_trackers", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "membership_feature_id", null: false
    t.date "period_start", null: false
    t.date "period_end", null: false
    t.integer "usage_count", default: 0
    t.boolean "limit_exceeded", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["limit_exceeded"], name: "index_usage_trackers_on_limit_exceeded"
    t.index ["membership_feature_id"], name: "index_usage_trackers_on_membership_feature_id"
    t.index ["period_start"], name: "index_usage_trackers_on_period_start"
    t.index ["user_id", "membership_feature_id", "period_start"], name: "index_usage_trackers_unique", unique: true
    t.index ["user_id"], name: "index_usage_trackers_on_user_id"
  end

  create_table "user_memberships", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "membership_tier_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "starts_at", null: false
    t.datetime "expires_at", null: false
    t.boolean "auto_renewal", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["expires_at"], name: "index_user_memberships_on_expires_at"
    t.index ["membership_tier_id"], name: "index_user_memberships_on_membership_tier_id"
    t.index ["starts_at"], name: "index_user_memberships_on_starts_at"
    t.index ["status"], name: "index_user_memberships_on_status"
    t.index ["user_id", "status"], name: "index_user_memberships_on_user_id_and_status"
    t.index ["user_id"], name: "index_user_memberships_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", null: false
    t.string "password_digest", null: false
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.string "phone_number", null: false
    t.date "birth_date", null: false
    t.datetime "verified_at"
    t.string "locale", default: "tr"
    t.string "timezone", default: "Europe/Istanbul"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["phone_number"], name: "index_users_on_phone_number"
    t.index ["verified_at"], name: "index_users_on_verified_at"
  end

  create_table "video_progresses", force: :cascade do |t|
    t.integer "course_video_id", null: false
    t.integer "patient_id", null: false
    t.integer "watched_seconds", default: 0, null: false
    t.decimal "progress_percentage", precision: 5, scale: 2, default: "0.0"
    t.boolean "completed", default: false
    t.datetime "first_watched_at"
    t.datetime "last_watched_at"
    t.datetime "completed_at"
    t.integer "watch_count", default: 0
    t.json "watch_sessions"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["completed"], name: "index_video_progresses_on_completed"
    t.index ["course_video_id", "patient_id"], name: "index_video_progresses_on_course_video_id_and_patient_id", unique: true
    t.index ["course_video_id"], name: "index_video_progresses_on_course_video_id"
    t.index ["last_watched_at"], name: "index_video_progresses_on_last_watched_at"
    t.index ["patient_id"], name: "index_video_progresses_on_patient_id"
    t.index ["progress_percentage"], name: "index_video_progresses_on_progress_percentage"
  end

  add_foreign_key "admins", "users"
  add_foreign_key "ai_health_insights", "doctors", column: "reviewed_by_id"
  add_foreign_key "ai_health_insights", "patients"
  add_foreign_key "appointments", "doctors"
  add_foreign_key "appointments", "patients"
  add_foreign_key "attachments", "users"
  add_foreign_key "calendar_integrations", "users"
  add_foreign_key "consultations", "appointments"
  add_foreign_key "consultations", "doctors"
  add_foreign_key "consultations", "patients"
  add_foreign_key "course_enrollments", "courses"
  add_foreign_key "course_enrollments", "patients"
  add_foreign_key "course_modules", "courses"
  add_foreign_key "course_videos", "course_modules"
  add_foreign_key "courses", "instructors"
  add_foreign_key "cycle_trackers", "patients"
  add_foreign_key "doctor_availabilities", "doctors"
  add_foreign_key "doctor_verifications", "doctors"
  add_foreign_key "doctors", "users"
  add_foreign_key "group_sessions", "hosts"
  add_foreign_key "health_metrics", "patients"
  add_foreign_key "health_profiles", "patients"
  add_foreign_key "medical_documents", "consultations"
  add_foreign_key "medical_documents", "patients"
  add_foreign_key "medical_documents", "users", column: "issued_by_id"
  add_foreign_key "medical_licenses", "doctors"
  add_foreign_key "notifications", "users"
  add_foreign_key "order_items", "orders"
  add_foreign_key "orders", "users"
  add_foreign_key "patients", "users"
  add_foreign_key "payment_methods", "users"
  add_foreign_key "payments", "orders"
  add_foreign_key "payments", "payment_methods"
  add_foreign_key "pregnancy_trackers", "patients"
  add_foreign_key "prescriptions", "consultations"
  add_foreign_key "prescriptions", "doctors"
  add_foreign_key "prescriptions", "patients"
  add_foreign_key "risk_assessments", "doctors", column: "assessed_by_id"
  add_foreign_key "risk_assessments", "patients"
  add_foreign_key "risk_assessments", "risk_assessments", column: "superseded_by_id"
  add_foreign_key "session_participants", "group_sessions"
  add_foreign_key "session_participants", "users"
  add_foreign_key "tier_features", "membership_features"
  add_foreign_key "tier_features", "membership_tiers"
  add_foreign_key "usage_trackers", "membership_features"
  add_foreign_key "usage_trackers", "users"
  add_foreign_key "user_memberships", "membership_tiers"
  add_foreign_key "user_memberships", "users"
  add_foreign_key "video_progresses", "course_videos"
  add_foreign_key "video_progresses", "patients"
end
