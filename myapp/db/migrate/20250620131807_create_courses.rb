class CreateCourses < ActiveRecord::Migration[8.0]
  def change
    create_table :courses do |t|
      t.references :instructor, null: false, foreign_key: true
      t.string :title, null: false
      t.text :description, null: false
      t.string :slug, null: false
      t.string :category, null: false
      t.string :difficulty_level, null: false
      t.integer :duration_minutes, default: 0
      t.decimal :price, precision: 8, scale: 2, default: 0.0
      t.string :currency, default: 'TRY'
      t.integer :status, default: 0, null: false
      t.datetime :published_at
      t.string :thumbnail_url
      t.string :preview_video_url
      t.json :learning_objectives
      t.json :prerequisites
      t.json :target_audience
      t.integer :enrollment_count, default: 0
      t.decimal :rating, precision: 3, scale: 2
      t.integer :review_count, default: 0
      t.boolean :is_featured, default: false
      t.integer :sort_order, default: 0

      t.timestamps
    end

    add_index :courses, :slug, unique: true
    add_index :courses, :category
    add_index :courses, :difficulty_level
    add_index :courses, :status
    add_index :courses, :published_at
    add_index :courses, :is_featured
    add_index :courses, :rating
    add_index :courses, [:instructor_id, :status]
  end
end
