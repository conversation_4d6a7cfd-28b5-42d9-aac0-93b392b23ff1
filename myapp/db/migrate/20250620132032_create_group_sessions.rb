class CreateGroupSessions < ActiveRecord::Migration[8.0]
  def change
    create_table :group_sessions do |t|
      t.references :host, null: false, foreign_key: true
      t.string :title, null: false
      t.text :description, null: false
      t.string :session_type, null: false
      t.datetime :scheduled_at, null: false
      t.datetime :ends_at, null: false
      t.integer :duration_minutes, null: false
      t.integer :max_participants, default: 10
      t.integer :current_participants, default: 0
      t.integer :status, default: 0, null: false
      t.decimal :price, precision: 8, scale: 2, default: 0.0
      t.string :currency, default: 'TRY'
      t.string :meeting_link
      t.string :meeting_id
      t.string :meeting_password
      t.datetime :started_at
      t.datetime :ended_at
      t.text :session_notes
      t.json :materials # Array of material objects
      t.boolean :recording_enabled, default: false
      t.string :recording_url

      t.timestamps
    end

    add_index :group_sessions, :scheduled_at
    add_index :group_sessions, :session_type
    add_index :group_sessions, :status
    add_index :group_sessions, [:host_id, :scheduled_at]
  end
end
