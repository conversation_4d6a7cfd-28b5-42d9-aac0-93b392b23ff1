Started GET "/" for 127.0.0.1 at 2025-06-20 15:46:13 +0300
  [1m[35m (3.8ms)[0m  [1m[35mCREATE TABLE "schema_migrations" ("version" varchar NOT NULL PRIMARY KEY) /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE TABLE "ar_internal_metadata" ("key" varchar NOT NULL PRIMARY KEY, "value" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.3ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
Processing by Rails::WelcomeController#index as HTML
  Rendering /home/<USER>/.rbenv/versions/3.3.0/lib/ruby/gems/3.3.0/gems/railties-8.0.2/lib/rails/templates/rails/welcome/index.html.erb
  Rendered /home/<USER>/.rbenv/versions/3.3.0/lib/ruby/gems/3.3.0/gems/railties-8.0.2/lib/rails/templates/rails/welcome/index.html.erb (Duration: 0.7ms | GC: 0.0ms)
Completed 200 OK in 22ms (Views: 5.5ms | ActiveRecord: 0.0ms (0 queries, 0 cached) | GC: 0.7ms)


Started GET "/icon.png" for 127.0.0.1 at 2025-06-20 15:46:14 +0300
  
ActionController::RoutingError (No route matches [GET] "/icon.png"):
  
Started GET "/icon.png" for 127.0.0.1 at 2025-06-20 15:46:14 +0300
  
ActionController::RoutingError (No route matches [GET] "/icon.png"):
  
  [1m[36mActiveRecord::InternalMetadata Load (0.3ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='Myapp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Create (1.7ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('environment', 'development', '2025-06-20 12:58:34.683482', '2025-06-20 12:58:34.683485') RETURNING "key" /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
Migrating to CreateUsers (**************)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE TABLE "users" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "email" varchar NOT NULL, "password_digest" varchar NOT NULL, "first_name" varchar NOT NULL, "last_name" varchar NOT NULL, "phone_number" varchar NOT NULL, "birth_date" date NOT NULL, "verified_at" datetime(6), "locale" varchar DEFAULT 'tr', "timezone" varchar DEFAULT 'Europe/Istanbul', "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_email" ON "users" ("email") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_users_on_phone_number" ON "users" ("phone_number") /*application='Myapp'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_users_on_verified_at" ON "users" ("verified_at") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.3ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('**************') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateAdmins (20250620125502)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.3ms)[0m  [1m[35mCREATE TABLE "admins" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "role" varchar DEFAULT 'admin' NOT NULL, "permissions" text, "department" varchar NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_378b9734e4"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_admins_on_user_id" ON "admins" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_admins_on_role" ON "admins" ("role") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_admins_on_user_id" ON "admins" ("user_id") /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[31mROLLBACK TRANSACTION /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (1.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.3ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.3ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.4ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
Migrating to CreateUsers (**************)
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35mSQL (0.2ms)[0m  [1m[35mDROP INDEX "index_users_on_verified_at" /*application='Myapp'*/[0m
  [1m[35mSQL (0.7ms)[0m  [1m[35mDROP INDEX "index_users_on_phone_number" /*application='Myapp'*/[0m
  [1m[35mSQL (0.5ms)[0m  [1m[35mDROP INDEX "index_users_on_email" /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mDROP TABLE "users" /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Destroy (0.2ms)[0m  [1m[31mDELETE FROM "schema_migrations" WHERE "schema_migrations"."version" = '**************' /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (9.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.5ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
Migrating to CreateUsers (**************)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (0.7ms)[0m  [1m[35mCREATE TABLE "users" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "email" varchar NOT NULL, "password_digest" varchar NOT NULL, "first_name" varchar NOT NULL, "last_name" varchar NOT NULL, "phone_number" varchar NOT NULL, "birth_date" date NOT NULL, "verified_at" datetime(6), "locale" varchar DEFAULT 'tr', "timezone" varchar DEFAULT 'Europe/Istanbul', "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_email" ON "users" ("email") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_users_on_phone_number" ON "users" ("phone_number") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_users_on_verified_at" ON "users" ("verified_at") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('**************') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (4.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateAdmins (20250620125502)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (0.8ms)[0m  [1m[35mCREATE TABLE "admins" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "role" varchar DEFAULT 'admin' NOT NULL, "permissions" text, "department" varchar NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_378b9734e4"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_admins_on_user_id" ON "admins" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_admins_on_role" ON "admins" ("role") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620125502') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateDoctors (20250620125511)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (3.1ms)[0m  [1m[35mCREATE TABLE "doctors" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "license_number" varchar NOT NULL, "specialization" varchar NOT NULL, "sub_specializations" text, "graduated_university" varchar NOT NULL, "graduation_year" integer NOT NULL, "current_institution" varchar, "years_experience" integer DEFAULT 0, "consultation_fee_day" decimal(8,2) NOT NULL, "consultation_fee_night" decimal(8,2) NOT NULL, "consultation_fee_emergency" decimal(8,2) NOT NULL, "languages_spoken" text, "bio" text, "verification_status" integer DEFAULT 0, "rating" decimal(3,2), "total_consultations" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_899b01ef33"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (1.2ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_user_id" ON "doctors" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_doctors_on_license_number" ON "doctors" ("license_number") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_specialization" ON "doctors" ("specialization") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_verification_status" ON "doctors" ("verification_status") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_rating" ON "doctors" ("rating") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620125511') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreatePatients (20250620125518)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.2ms)[0m  [1m[35mCREATE TABLE "patients" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "medical_record_number" varchar NOT NULL, "emergency_contact_name" varchar NOT NULL, "emergency_contact_phone" varchar NOT NULL, "blood_type" varchar, "pregnancy_count" integer DEFAULT 0, "birth_count" integer DEFAULT 0, "smoking_status" integer DEFAULT 0, "alcohol_consumption" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_623f05c630"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_patients_on_user_id" ON "patients" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_patients_on_medical_record_number" ON "patients" ("medical_record_number") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_patients_on_blood_type" ON "patients" ("blood_type") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620125518') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
Started GET "/" for 127.0.0.1 at 2025-06-20 15:59:47 +0300
  [1m[36mActiveRecord::SchemaMigration Load (0.3ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  
ActiveRecord::PendingMigrationError (

Migrations are pending. To resolve this issue, run:

        bin/rails db:migrate

You have 1 pending migration:

db/migrate/20250620125947_create_membership_features.rb


):
  
activerecord (8.0.2) lib/active_record/migration.rb:743:in `check_pending_migrations'
activerecord (8.0.2) lib/active_record/migration.rb:660:in `block (2 levels) in call'
activesupport (8.0.2) lib/active_support/file_update_checker.rb:85:in `execute'
activesupport (8.0.2) lib/active_support/file_update_checker.rb:95:in `execute_if_updated'
activerecord (8.0.2) lib/active_record/migration.rb:667:in `block in call'
activerecord (8.0.2) lib/active_record/migration.rb:657:in `synchronize'
activerecord (8.0.2) lib/active_record/migration.rb:657:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/callbacks.rb:31:in `block in call'
activesupport (8.0.2) lib/active_support/callbacks.rb:100:in `run_callbacks'
actionpack (8.0.2) lib/action_dispatch/middleware/callbacks.rb:30:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/executor.rb:16:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/actionable_exceptions.rb:18:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/debug_exceptions.rb:31:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/show_exceptions.rb:32:in `call'
railties (8.0.2) lib/rails/rack/logger.rb:41:in `call_app'
railties (8.0.2) lib/rails/rack/logger.rb:29:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/remote_ip.rb:96:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/request_id.rb:34:in `call'
rack (3.1.16) lib/rack/runtime.rb:24:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/server_timing.rb:61:in `block in call'
actionpack (8.0.2) lib/action_dispatch/middleware/server_timing.rb:26:in `collect_events'
actionpack (8.0.2) lib/action_dispatch/middleware/server_timing.rb:60:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/executor.rb:16:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/static.rb:27:in `call'
rack (3.1.16) lib/rack/sendfile.rb:114:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/host_authorization.rb:143:in `call'
railties (8.0.2) lib/rails/engine.rb:535:in `call'
puma (6.6.0) lib/puma/configuration.rb:279:in `call'
puma (6.6.0) lib/puma/request.rb:99:in `block in handle_request'
puma (6.6.0) lib/puma/thread_pool.rb:390:in `with_force_shutdown'
puma (6.6.0) lib/puma/request.rb:98:in `handle_request'
puma (6.6.0) lib/puma/server.rb:472:in `process_client'
puma (6.6.0) lib/puma/server.rb:254:in `block in run'
puma (6.6.0) lib/puma/thread_pool.rb:167:in `block in spawn_thread'
Started GET "/favicon.ico" for 127.0.0.1 at 2025-06-20 15:59:47 +0300
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  
ActiveRecord::PendingMigrationError (

Migrations are pending. To resolve this issue, run:

        bin/rails db:migrate

You have 1 pending migration:

db/migrate/20250620125947_create_membership_features.rb


):
  
activerecord (8.0.2) lib/active_record/migration.rb:743:in `check_pending_migrations'
activerecord (8.0.2) lib/active_record/migration.rb:660:in `block (2 levels) in call'
activesupport (8.0.2) lib/active_support/file_update_checker.rb:85:in `execute'
activerecord (8.0.2) lib/active_record/migration.rb:665:in `block in call'
activerecord (8.0.2) lib/active_record/migration.rb:657:in `synchronize'
activerecord (8.0.2) lib/active_record/migration.rb:657:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/callbacks.rb:31:in `block in call'
activesupport (8.0.2) lib/active_support/callbacks.rb:100:in `run_callbacks'
actionpack (8.0.2) lib/action_dispatch/middleware/callbacks.rb:30:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/executor.rb:16:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/actionable_exceptions.rb:18:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/debug_exceptions.rb:31:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/show_exceptions.rb:32:in `call'
railties (8.0.2) lib/rails/rack/logger.rb:41:in `call_app'
railties (8.0.2) lib/rails/rack/logger.rb:29:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/remote_ip.rb:96:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/request_id.rb:34:in `call'
rack (3.1.16) lib/rack/runtime.rb:24:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/server_timing.rb:61:in `block in call'
actionpack (8.0.2) lib/action_dispatch/middleware/server_timing.rb:26:in `collect_events'
actionpack (8.0.2) lib/action_dispatch/middleware/server_timing.rb:60:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/executor.rb:16:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/static.rb:27:in `call'
rack (3.1.16) lib/rack/sendfile.rb:114:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/host_authorization.rb:143:in `call'
railties (8.0.2) lib/rails/engine.rb:535:in `call'
puma (6.6.0) lib/puma/configuration.rb:279:in `call'
puma (6.6.0) lib/puma/request.rb:99:in `block in handle_request'
puma (6.6.0) lib/puma/thread_pool.rb:390:in `with_force_shutdown'
puma (6.6.0) lib/puma/request.rb:98:in `handle_request'
puma (6.6.0) lib/puma/server.rb:472:in `process_client'
puma (6.6.0) lib/puma/server.rb:254:in `block in run'
puma (6.6.0) lib/puma/thread_pool.rb:167:in `block in spawn_thread'
  [1m[36mUser Load (5.3ms)[0m  [1m[34mSELECT "users".* FROM "users" /* loading for pp */ LIMIT 11 /*application='Myapp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
Migrating to CreateMembershipFeatures (20250620125947)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE TABLE "membership_features" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "feature_key" varchar NOT NULL, "description" text NOT NULL, "feature_type" integer DEFAULT 0 NOT NULL, "category" varchar NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_membership_features_on_feature_key" ON "membership_features" ("feature_key") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_membership_features_on_category" ON "membership_features" ("category") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_membership_features_on_feature_type" ON "membership_features" ("feature_type") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620125947') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (4.6ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateMembershipTiers (20250620125956)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.0ms)[0m  [1m[35mCREATE TABLE "membership_tiers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "slug" varchar NOT NULL, "description" text NOT NULL, "price" decimal(8,2) DEFAULT 0.0 NOT NULL, "currency" varchar DEFAULT 'TRY' NOT NULL, "billing_cycle" integer DEFAULT 1 NOT NULL, "trial_days" integer DEFAULT 0, "sort_order" integer NOT NULL, "active" boolean DEFAULT 1, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_membership_tiers_on_slug" ON "membership_tiers" ("slug") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_membership_tiers_on_active" ON "membership_tiers" ("active") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_membership_tiers_on_sort_order" ON "membership_tiers" ("sort_order") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_membership_tiers_on_price" ON "membership_tiers" ("price") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620125956') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateTierFeatures (20250620130013)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (6.6ms)[0m  [1m[35mCREATE TABLE "tier_features" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "membership_tier_id" integer NOT NULL, "membership_feature_id" integer NOT NULL, "limit_value" integer, "is_unlimited" boolean DEFAULT 0, "is_included" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_39701094c9"
FOREIGN KEY ("membership_tier_id")
  REFERENCES "membership_tiers" ("id")
, CONSTRAINT "fk_rails_605fb37803"
FOREIGN KEY ("membership_feature_id")
  REFERENCES "membership_features" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_membership_tier_id" ON "tier_features" ("membership_tier_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_membership_feature_id" ON "tier_features" ("membership_feature_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_tier_features_on_tier_and_feature" ON "tier_features" ("membership_tier_id", "membership_feature_id") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_is_included" ON "tier_features" ("is_included") /*application='Myapp'*/[0m
  [1m[35m (1.2ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_is_unlimited" ON "tier_features" ("is_unlimited") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.7ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620130013') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.9ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateUserMemberships (20250620130022)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (2.7ms)[0m  [1m[35mCREATE TABLE "user_memberships" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "membership_tier_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "starts_at" datetime(6) NOT NULL, "expires_at" datetime(6) NOT NULL, "auto_renewal" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_e670a4dd3b"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
, CONSTRAINT "fk_rails_a2f75c886b"
FOREIGN KEY ("membership_tier_id")
  REFERENCES "membership_tiers" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_user_id" ON "user_memberships" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (1.0ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_membership_tier_id" ON "user_memberships" ("membership_tier_id") /*application='Myapp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_status" ON "user_memberships" ("status") /*application='Myapp'*/[0m
  [1m[35m (0.7ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_starts_at" ON "user_memberships" ("starts_at") /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_expires_at" ON "user_memberships" ("expires_at") /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_user_id_and_status" ON "user_memberships" ("user_id", "status") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.5ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620130022') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateUsageTrackers (20250620130032)
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (4.1ms)[0m  [1m[35mCREATE TABLE "usage_trackers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "membership_feature_id" integer NOT NULL, "period_start" date NOT NULL, "period_end" date NOT NULL, "usage_count" integer DEFAULT 0, "limit_exceeded" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_d413b57732"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
, CONSTRAINT "fk_rails_4cba118828"
FOREIGN KEY ("membership_feature_id")
  REFERENCES "membership_features" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_user_id" ON "usage_trackers" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_membership_feature_id" ON "usage_trackers" ("membership_feature_id") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_usage_trackers_unique" ON "usage_trackers" ("user_id", "membership_feature_id", "period_start") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_period_start" ON "usage_trackers" ("period_start") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_limit_exceeded" ON "usage_trackers" ("limit_exceeded") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.4ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620130032') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.6ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
Migrating to CreateDoctorAvailabilities (20250620130658)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (5.2ms)[0m  [1m[35mCREATE TABLE "doctor_availabilities" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "doctor_id" integer NOT NULL, "day_of_week" integer NOT NULL, "start_time" varchar NOT NULL, "end_time" varchar NOT NULL, "appointment_duration" integer DEFAULT 30 NOT NULL, "buffer_time" integer DEFAULT 5, "max_appointments" integer DEFAULT 16 NOT NULL, "is_available" boolean DEFAULT 1, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_ca70b6a810"
FOREIGN KEY ("doctor_id")
  REFERENCES "doctors" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_doctor_availabilities_on_doctor_id" ON "doctor_availabilities" ("doctor_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_doctor_availabilities_on_doctor_id_and_day_of_week" ON "doctor_availabilities" ("doctor_id", "day_of_week") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_doctor_availabilities_on_day_of_week" ON "doctor_availabilities" ("day_of_week") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_doctor_availabilities_on_is_available" ON "doctor_availabilities" ("is_available") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620130658') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (2.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateMedicalLicenses (20250620130706)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (2.2ms)[0m  [1m[35mCREATE TABLE "medical_licenses" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "doctor_id" integer NOT NULL, "license_number" varchar NOT NULL, "issuing_authority" varchar NOT NULL, "issue_date" date NOT NULL, "expiry_date" date NOT NULL, "license_type" varchar NOT NULL, "status" integer DEFAULT 0, "document_url" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_ecc8c5f96a"
FOREIGN KEY ("doctor_id")
  REFERENCES "doctors" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_licenses_on_doctor_id" ON "medical_licenses" ("doctor_id") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_medical_licenses_on_license_number" ON "medical_licenses" ("license_number") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_medical_licenses_on_status" ON "medical_licenses" ("status") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_medical_licenses_on_expiry_date" ON "medical_licenses" ("expiry_date") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_medical_licenses_on_license_type" ON "medical_licenses" ("license_type") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620130706') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateDoctorVerifications (20250620130715)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.9ms)[0m  [1m[35mCREATE TABLE "doctor_verifications" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "doctor_id" integer NOT NULL, "verification_type" varchar, "status" integer, "submitted_at" datetime(6), "reviewed_at" datetime(6), "reviewer_notes" text, "documents" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_7ea1e54b91"
FOREIGN KEY ("doctor_id")
  REFERENCES "doctors" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_doctor_verifications_on_doctor_id" ON "doctor_verifications" ("doctor_id") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620130715') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateHealthProfiles (20250620130725)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (2.2ms)[0m  [1m[35mCREATE TABLE "health_profiles" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "height" decimal, "weight" decimal, "allergies" text, "chronic_conditions" text, "medications" text, "family_history" text, "lifestyle_notes" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_9630807551"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_health_profiles_on_patient_id" ON "health_profiles" ("patient_id") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.3ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620130725') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateAppointments (20250620131618)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.2ms)[0m  [1m[35mCREATE TABLE "appointments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "appointment_type" varchar NOT NULL, "scheduled_at" datetime(6) NOT NULL, "ends_at" datetime(6) NOT NULL, "status" integer DEFAULT 0 NOT NULL, "duration_minutes" integer DEFAULT 30 NOT NULL, "consultation_fee" decimal(8,2) NOT NULL, "patient_notes" text, "doctor_notes" text, "meeting_link" varchar, "meeting_id" varchar, "started_at" datetime(6), "ended_at" datetime(6), "cancellation_reason" varchar, "cancelled_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_c63da04ab4"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
, CONSTRAINT "fk_rails_8db8e1e8a5"
FOREIGN KEY ("doctor_id")
  REFERENCES "doctors" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_patient_id" ON "appointments" ("patient_id") /*application='Myapp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_doctor_id" ON "appointments" ("doctor_id") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_scheduled_at" ON "appointments" ("scheduled_at") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_status" ON "appointments" ("status") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_appointment_type" ON "appointments" ("appointment_type") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_patient_id_and_scheduled_at" ON "appointments" ("patient_id", "scheduled_at") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_doctor_id_and_scheduled_at" ON "appointments" ("doctor_id", "scheduled_at") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.4ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620131618') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.7ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateConsultations (20250620131703)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.7ms)[0m  [1m[35mCREATE TABLE "consultations" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "appointment_id" integer, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "consultation_type" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "started_at" datetime(6), "ended_at" datetime(6), "duration_minutes" integer, "chief_complaint" text NOT NULL, "history_of_present_illness" text, "physical_examination" text, "assessment" text, "plan" text, "follow_up_instructions" text, "additional_notes" text, "consultation_fee" decimal(8,2), "consultation_method" varchar, "vital_signs" json, "symptoms" json, "prescription_issued" boolean DEFAULT 0, "next_follow_up_date" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_04612d29f0"
FOREIGN KEY ("appointment_id")
  REFERENCES "appointments" ("id")
, CONSTRAINT "fk_rails_33c52f1c05"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
, CONSTRAINT "fk_rails_b1f629cdac"
FOREIGN KEY ("doctor_id")
  REFERENCES "doctors" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_appointment_id" ON "consultations" ("appointment_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_patient_id" ON "consultations" ("patient_id") /*application='Myapp'*/[0m
  [1m[35m (0.7ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_doctor_id" ON "consultations" ("doctor_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_started_at" ON "consultations" ("started_at") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_status" ON "consultations" ("status") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_consultation_type" ON "consultations" ("consultation_type") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_patient_id_and_started_at" ON "consultations" ("patient_id", "started_at") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_doctor_id_and_started_at" ON "consultations" ("doctor_id", "started_at") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_next_follow_up_date" ON "consultations" ("next_follow_up_date") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.7ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620131703') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.9ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreatePrescriptions (20250620131734)
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (3.2ms)[0m  [1m[35mCREATE TABLE "prescriptions" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "consultation_id" integer NOT NULL, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "prescription_number" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6) NOT NULL, "instructions" text, "diagnosis" text, "additional_notes" text, "medications" json, "is_digital" boolean DEFAULT 1, "pharmacy_name" varchar, "pharmacy_address" varchar, "dispensed_at" datetime(6), "dispensed_by" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_280b58d894"
FOREIGN KEY ("consultation_id")
  REFERENCES "consultations" ("id")
, CONSTRAINT "fk_rails_bede94f0a0"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
, CONSTRAINT "fk_rails_f06942fc64"
FOREIGN KEY ("doctor_id")
  REFERENCES "doctors" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_consultation_id" ON "prescriptions" ("consultation_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_patient_id" ON "prescriptions" ("patient_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_doctor_id" ON "prescriptions" ("doctor_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_prescriptions_on_prescription_number" ON "prescriptions" ("prescription_number") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_issued_at" ON "prescriptions" ("issued_at") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_expires_at" ON "prescriptions" ("expires_at") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_status" ON "prescriptions" ("status") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_patient_id_and_issued_at" ON "prescriptions" ("patient_id", "issued_at") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_doctor_id_and_issued_at" ON "prescriptions" ("doctor_id", "issued_at") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.4ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620131734') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateCourses (20250620131807)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (2.6ms)[0m  [1m[35mCREATE TABLE "courses" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "instructor_id" integer NOT NULL, "title" varchar NOT NULL, "description" text NOT NULL, "slug" varchar NOT NULL, "category" varchar NOT NULL, "difficulty_level" varchar NOT NULL, "duration_minutes" integer DEFAULT 0, "price" decimal(8,2) DEFAULT 0.0, "currency" varchar DEFAULT 'TRY', "status" integer DEFAULT 0 NOT NULL, "published_at" datetime(6), "thumbnail_url" varchar, "preview_video_url" varchar, "learning_objectives" json, "prerequisites" json, "target_audience" json, "enrollment_count" integer DEFAULT 0, "rating" decimal(3,2), "review_count" integer DEFAULT 0, "is_featured" boolean DEFAULT 0, "sort_order" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_2ab3132eb0"
FOREIGN KEY ("instructor_id")
  REFERENCES "instructors" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_courses_on_instructor_id" ON "courses" ("instructor_id") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_courses_on_slug" ON "courses" ("slug") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_courses_on_category" ON "courses" ("category") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_courses_on_difficulty_level" ON "courses" ("difficulty_level") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_courses_on_status" ON "courses" ("status") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_published_at" ON "courses" ("published_at") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_is_featured" ON "courses" ("is_featured") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_rating" ON "courses" ("rating") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_instructor_id_and_status" ON "courses" ("instructor_id", "status") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620131807') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.9ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateCourseModules (20250620131835)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (0.9ms)[0m  [1m[35mCREATE TABLE "course_modules" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_id" integer NOT NULL, "title" varchar NOT NULL, "description" text, "sort_order" integer NOT NULL, "duration_minutes" integer DEFAULT 0, "is_published" boolean DEFAULT 0, "learning_objectives" json, "content_summary" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_74391d7a5f"
FOREIGN KEY ("course_id")
  REFERENCES "courses" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_modules_on_course_id" ON "course_modules" ("course_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_course_modules_on_course_id_and_sort_order" ON "course_modules" ("course_id", "sort_order") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_course_modules_on_is_published" ON "course_modules" ("is_published") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.4ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620131835') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.8ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateCourseVideos (20250620131900)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (0.9ms)[0m  [1m[35mCREATE TABLE "course_videos" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_module_id" integer NOT NULL, "title" varchar NOT NULL, "description" text, "video_url" varchar NOT NULL, "thumbnail_url" varchar, "duration_seconds" integer NOT NULL, "sort_order" integer NOT NULL, "is_published" boolean DEFAULT 0, "is_preview" boolean DEFAULT 0, "video_quality" varchar, "file_size_bytes" bigint, "subtitles" json, "video_metadata" json, "view_count" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_3d40bb2a3c"
FOREIGN KEY ("course_module_id")
  REFERENCES "course_modules" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_videos_on_course_module_id" ON "course_videos" ("course_module_id") /*application='Myapp'*/[0m
  [1m[35m (0.8ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_course_videos_on_course_module_id_and_sort_order" ON "course_videos" ("course_module_id", "sort_order") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_course_videos_on_is_published" ON "course_videos" ("is_published") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_course_videos_on_is_preview" ON "course_videos" ("is_preview") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620131900') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateCourseEnrollments (20250620131933)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.4ms)[0m  [1m[35mCREATE TABLE "course_enrollments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_id" integer NOT NULL, "patient_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "enrolled_at" datetime(6) NOT NULL, "completed_at" datetime(6), "last_accessed_at" datetime(6), "progress_percentage" decimal(5,2) DEFAULT 0.0, "total_watch_time_seconds" integer DEFAULT 0, "rating" decimal(3,2), "review" text, "review_submitted_at" datetime(6), "certificate_issued" boolean DEFAULT 0, "certificate_issued_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_875ed9c80f"
FOREIGN KEY ("course_id")
  REFERENCES "courses" ("id")
, CONSTRAINT "fk_rails_c6946b5a3d"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_course_id" ON "course_enrollments" ("course_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_patient_id" ON "course_enrollments" ("patient_id") /*application='Myapp'*/[0m
  [1m[35m (1.4ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_course_enrollments_on_course_id_and_patient_id" ON "course_enrollments" ("course_id", "patient_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_status" ON "course_enrollments" ("status") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_enrolled_at" ON "course_enrollments" ("enrolled_at") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_completed_at" ON "course_enrollments" ("completed_at") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_progress_percentage" ON "course_enrollments" ("progress_percentage") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620131933') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateVideoProgresses (20250620132002)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.0ms)[0m  [1m[35mCREATE TABLE "video_progresses" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_video_id" integer NOT NULL, "patient_id" integer NOT NULL, "watched_seconds" integer DEFAULT 0 NOT NULL, "progress_percentage" decimal(5,2) DEFAULT 0.0, "completed" boolean DEFAULT 0, "first_watched_at" datetime(6), "last_watched_at" datetime(6), "completed_at" datetime(6), "watch_count" integer DEFAULT 0, "watch_sessions" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_c494bbcd05"
FOREIGN KEY ("course_video_id")
  REFERENCES "course_videos" ("id")
, CONSTRAINT "fk_rails_d3cf50c204"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_course_video_id" ON "video_progresses" ("course_video_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_patient_id" ON "video_progresses" ("patient_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_video_progresses_on_course_video_id_and_patient_id" ON "video_progresses" ("course_video_id", "patient_id") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_completed" ON "video_progresses" ("completed") /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_last_watched_at" ON "video_progresses" ("last_watched_at") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_progress_percentage" ON "video_progresses" ("progress_percentage") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.4ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132002') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateGroupSessions (20250620132032)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.8ms)[0m  [1m[35mCREATE TABLE "group_sessions" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "host_id" integer NOT NULL, "title" varchar NOT NULL, "description" text NOT NULL, "session_type" varchar NOT NULL, "scheduled_at" datetime(6) NOT NULL, "ends_at" datetime(6) NOT NULL, "duration_minutes" integer NOT NULL, "max_participants" integer DEFAULT 10, "current_participants" integer DEFAULT 0, "status" integer DEFAULT 0 NOT NULL, "price" decimal(8,2) DEFAULT 0.0, "currency" varchar DEFAULT 'TRY', "meeting_link" varchar, "meeting_id" varchar, "meeting_password" varchar, "started_at" datetime(6), "ended_at" datetime(6), "session_notes" text, "materials" json, "recording_enabled" boolean DEFAULT 0, "recording_url" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_f149660cd5"
FOREIGN KEY ("host_id")
  REFERENCES "hosts" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_group_sessions_on_host_id" ON "group_sessions" ("host_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_group_sessions_on_scheduled_at" ON "group_sessions" ("scheduled_at") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_group_sessions_on_session_type" ON "group_sessions" ("session_type") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_group_sessions_on_status" ON "group_sessions" ("status") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_group_sessions_on_host_id_and_scheduled_at" ON "group_sessions" ("host_id", "scheduled_at") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132032') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateSessionParticipants (20250620132059)
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.8ms)[0m  [1m[35mCREATE TABLE "session_participants" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "group_session_id" integer NOT NULL, "user_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "registered_at" datetime(6) NOT NULL, "joined_at" datetime(6), "left_at" datetime(6), "total_duration_seconds" integer DEFAULT 0, "attended" boolean DEFAULT 0, "attendance_percentage" decimal(5,2) DEFAULT 0.0, "feedback" text, "rating" decimal(3,2), "feedback_submitted_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_f197fe06a4"
FOREIGN KEY ("group_session_id")
  REFERENCES "group_sessions" ("id")
, CONSTRAINT "fk_rails_1d0ee5bc82"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_group_session_id" ON "session_participants" ("group_session_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_user_id" ON "session_participants" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_session_participants_on_group_session_id_and_user_id" ON "session_participants" ("group_session_id", "user_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_status" ON "session_participants" ("status") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_registered_at" ON "session_participants" ("registered_at") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_attended" ON "session_participants" ("attended") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132059') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateCycleTrackers (20250620132134)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (2.1ms)[0m  [1m[35mCREATE TABLE "cycle_trackers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "cycle_start_date" date NOT NULL, "cycle_end_date" date, "predicted_next_cycle" date, "cycle_length" integer, "period_length" integer, "flow_intensity" varchar, "symptoms" json, "mood_data" json, "notes" text, "is_irregular" boolean DEFAULT 0, "basal_body_temperature" decimal(4,2), "cervical_mucus_type" varchar, "ovulation_detected" boolean DEFAULT 0, "ovulation_date" date, "fertility_signs" json, "contraception_used" boolean DEFAULT 0, "contraception_type" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_1f5eb00fc5"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_cycle_trackers_on_patient_id" ON "cycle_trackers" ("patient_id") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_cycle_trackers_on_cycle_start_date" ON "cycle_trackers" ("cycle_start_date") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_cycle_trackers_on_predicted_next_cycle" ON "cycle_trackers" ("predicted_next_cycle") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_cycle_trackers_on_ovulation_date" ON "cycle_trackers" ("ovulation_date") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_cycle_trackers_on_patient_id_and_cycle_start_date" ON "cycle_trackers" ("patient_id", "cycle_start_date") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132134') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreatePregnancyTrackers (20250620132202)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (0.9ms)[0m  [1m[35mCREATE TABLE "pregnancy_trackers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "conception_date" date NOT NULL, "due_date" date NOT NULL, "last_menstrual_period" date, "current_week" integer DEFAULT 0, "current_trimester" integer DEFAULT 1, "pre_pregnancy_weight" decimal(5,2), "current_weight" decimal(5,2), "weight_gain" decimal(5,2) DEFAULT 0.0, "fundal_height" decimal(4,1), "fetal_heart_rate" integer, "blood_pressure_systolic" integer, "blood_pressure_diastolic" integer, "glucose_level" decimal(5,2), "iron_level" decimal(5,2), "symptoms" json, "risk_factors" json, "notes" text, "active" boolean DEFAULT 1, "pregnancy_type" varchar DEFAULT 'singleton', "delivery_date" datetime(6), "delivery_type" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_4714aeefbb"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_patient_id" ON "pregnancy_trackers" ("patient_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_due_date" ON "pregnancy_trackers" ("due_date") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_current_week" ON "pregnancy_trackers" ("current_week") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_current_trimester" ON "pregnancy_trackers" ("current_trimester") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_active" ON "pregnancy_trackers" ("active") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_patient_id_and_active" ON "pregnancy_trackers" ("patient_id", "active") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132202') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.9ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateHealthMetrics (20250620132231)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (2.1ms)[0m  [1m[35mCREATE TABLE "health_metrics" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "trackable_type" varchar, "trackable_id" integer, "metric_type" varchar NOT NULL, "value" decimal(10,4) NOT NULL, "unit" varchar NOT NULL, "recorded_at" datetime(6) NOT NULL, "source" varchar NOT NULL, "notes" text, "metadata" json, "device_id" varchar, "reference_min" decimal(10,4), "reference_max" decimal(10,4), "is_abnormal" boolean DEFAULT 0, "trend" varchar, "quality_score" integer, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_fcc1125f48"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_patient_id" ON "health_metrics" ("patient_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_trackable" ON "health_metrics" ("trackable_type", "trackable_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_metric_type" ON "health_metrics" ("metric_type") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_recorded_at" ON "health_metrics" ("recorded_at") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_source" ON "health_metrics" ("source") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_is_abnormal" ON "health_metrics" ("is_abnormal") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "idx_on_patient_id_metric_type_recorded_at_d2a57a400b" ON "health_metrics" ("patient_id", "metric_type", "recorded_at") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_trackable_type_and_trackable_id" ON "health_metrics" ("trackable_type", "trackable_id") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132231') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateAiHealthInsights (20250620132300)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (0.8ms)[0m  [1m[35mCREATE TABLE "ai_health_insights" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "insight_type" varchar NOT NULL, "title" varchar NOT NULL, "content" text NOT NULL, "summary" text, "confidence_score" decimal(5,4) NOT NULL, "generated_at" datetime(6) NOT NULL, "status" integer DEFAULT 0 NOT NULL, "priority" integer DEFAULT 1, "data_sources" json, "recommendations" json, "metadata" json, "ai_model_version" varchar, "reviewed_by_doctor" boolean DEFAULT 0, "reviewed_by_id" integer, "reviewed_at" datetime(6), "doctor_notes" text, "patient_acknowledged" boolean DEFAULT 0, "acknowledged_at" datetime(6), "expires_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_f47084857d"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
, CONSTRAINT "fk_rails_3fd0652fab"
FOREIGN KEY ("reviewed_by_id")
  REFERENCES "doctors" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_patient_id" ON "ai_health_insights" ("patient_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_reviewed_by_id" ON "ai_health_insights" ("reviewed_by_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_insight_type" ON "ai_health_insights" ("insight_type") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_generated_at" ON "ai_health_insights" ("generated_at") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_status" ON "ai_health_insights" ("status") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_priority" ON "ai_health_insights" ("priority") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_confidence_score" ON "ai_health_insights" ("confidence_score") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_reviewed_by_doctor" ON "ai_health_insights" ("reviewed_by_doctor") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_patient_id_and_generated_at" ON "ai_health_insights" ("patient_id", "generated_at") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132300') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateRiskAssessments (20250620132329)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (2.7ms)[0m  [1m[35mCREATE TABLE "risk_assessments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "assessed_by_id" integer, "assessment_type" varchar NOT NULL, "risk_level" varchar NOT NULL, "risk_score" decimal(5,2) NOT NULL, "risk_factors" json, "protective_factors" json, "clinical_notes" text, "recommendations" json, "assessed_at" datetime(6) NOT NULL, "valid_until" datetime(6), "requires_follow_up" boolean DEFAULT 0, "next_assessment_due" datetime(6), "assessment_method" varchar, "assessment_data" json, "status" integer DEFAULT 0, "superseded_by_id" integer, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_6abf1d6d6d"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
, CONSTRAINT "fk_rails_94a65cd14d"
FOREIGN KEY ("assessed_by_id")
  REFERENCES "doctors" ("id")
, CONSTRAINT "fk_rails_129ec8ae92"
FOREIGN KEY ("superseded_by_id")
  REFERENCES "risk_assessments" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_patient_id" ON "risk_assessments" ("patient_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_assessed_by_id" ON "risk_assessments" ("assessed_by_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_superseded_by_id" ON "risk_assessments" ("superseded_by_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_assessment_type" ON "risk_assessments" ("assessment_type") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_risk_level" ON "risk_assessments" ("risk_level") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_assessed_at" ON "risk_assessments" ("assessed_at") /*application='Myapp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_valid_until" ON "risk_assessments" ("valid_until") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_requires_follow_up" ON "risk_assessments" ("requires_follow_up") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_next_assessment_due" ON "risk_assessments" ("next_assessment_due") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "idx_on_patient_id_assessment_type_assessed_at_c1a3b8d791" ON "risk_assessments" ("patient_id", "assessment_type", "assessed_at") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132329') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreatePaymentMethods (20250620132516)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (0.8ms)[0m  [1m[35mCREATE TABLE "payment_methods" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "payment_type" varchar NOT NULL, "provider" varchar NOT NULL, "token" varchar NOT NULL, "last_four" varchar NOT NULL, "cardholder_name" varchar, "expires_at" date, "billing_address_line1" varchar, "billing_address_line2" varchar, "billing_city" varchar, "billing_state" varchar, "billing_postal_code" varchar, "billing_country" varchar DEFAULT 'TR', "is_default" boolean DEFAULT 0, "active" boolean DEFAULT 1, "verified_at" datetime(6), "metadata" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_e13d4c515f"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_user_id" ON "payment_methods" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_payment_type" ON "payment_methods" ("payment_type") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_provider" ON "payment_methods" ("provider") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_is_default" ON "payment_methods" ("is_default") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_active" ON "payment_methods" ("active") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_user_id_and_is_default" ON "payment_methods" ("user_id", "is_default") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132516') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.7ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateNotifications (20250620132544)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.0ms)[0m  [1m[35mCREATE TABLE "notifications" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "notifiable_type" varchar, "notifiable_id" integer, "notification_type" varchar NOT NULL, "title" varchar NOT NULL, "message" text NOT NULL, "action_text" text, "action_url" varchar, "read_at" datetime(6), "sent_at" datetime(6) NOT NULL, "priority" integer DEFAULT 1 NOT NULL, "delivery_method" varchar, "delivered" boolean DEFAULT 0, "delivered_at" datetime(6), "delivery_metadata" json, "expires_at" datetime(6), "icon" varchar, "color" varchar, "data" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_b080fb4855"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_user_id" ON "notifications" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_notifiable" ON "notifications" ("notifiable_type", "notifiable_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_notification_type" ON "notifications" ("notification_type") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_read_at" ON "notifications" ("read_at") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_sent_at" ON "notifications" ("sent_at") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_priority" ON "notifications" ("priority") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_delivered" ON "notifications" ("delivered") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_expires_at" ON "notifications" ("expires_at") /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_user_id_and_read_at" ON "notifications" ("user_id", "read_at") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_notifiable_type_and_notifiable_id" ON "notifications" ("notifiable_type", "notifiable_id") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132544') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateMedicalDocuments (20250620132612)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (2.7ms)[0m  [1m[35mCREATE TABLE "medical_documents" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "issued_by_id" integer, "consultation_id" integer, "document_type" varchar NOT NULL, "title" varchar NOT NULL, "description" text, "file_url" varchar NOT NULL, "file_name" varchar, "file_type" varchar, "file_size_bytes" bigint, "file_hash" varchar, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6), "status" integer DEFAULT 0 NOT NULL, "is_sensitive" boolean DEFAULT 0, "access_permissions" json, "metadata" json, "external_id" varchar, "institution_name" varchar, "last_accessed_at" datetime(6), "access_count" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_32eea0a133"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
, CONSTRAINT "fk_rails_9505f7c2d6"
FOREIGN KEY ("issued_by_id")
  REFERENCES "users" ("id")
, CONSTRAINT "fk_rails_28782abca9"
FOREIGN KEY ("consultation_id")
  REFERENCES "consultations" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_patient_id" ON "medical_documents" ("patient_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_issued_by_id" ON "medical_documents" ("issued_by_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_consultation_id" ON "medical_documents" ("consultation_id") /*application='Myapp'*/[0m
  [1m[35m (1.3ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_document_type" ON "medical_documents" ("document_type") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_issued_at" ON "medical_documents" ("issued_at") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_expires_at" ON "medical_documents" ("expires_at") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_status" ON "medical_documents" ("status") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_is_sensitive" ON "medical_documents" ("is_sensitive") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_patient_id_and_document_type" ON "medical_documents" ("patient_id", "document_type") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_medical_documents_on_file_hash" ON "medical_documents" ("file_hash") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132612') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateAttachments (20250620132720)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (2.3ms)[0m  [1m[35mCREATE TABLE "attachments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "attachable_type" varchar NOT NULL, "attachable_id" integer NOT NULL, "user_id" integer NOT NULL, "file_name" varchar NOT NULL, "original_file_name" varchar NOT NULL, "file_url" varchar NOT NULL, "file_type" varchar NOT NULL, "file_size_bytes" bigint NOT NULL, "file_hash" varchar, "mime_type" varchar, "description" text, "is_public" boolean DEFAULT 0, "is_processed" boolean DEFAULT 0, "processing_metadata" json, "thumbnail_url" varchar, "uploaded_at" datetime(6) NOT NULL, "last_accessed_at" datetime(6), "download_count" integer DEFAULT 0, "expires_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_5650a5e7db"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_attachable" ON "attachments" ("attachable_type", "attachable_id") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_user_id" ON "attachments" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_attachable_type_and_attachable_id" ON "attachments" ("attachable_type", "attachable_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_file_type" ON "attachments" ("file_type") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_uploaded_at" ON "attachments" ("uploaded_at") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_is_public" ON "attachments" ("is_public") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_attachments_on_file_hash" ON "attachments" ("file_hash") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132720') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.8ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateCalendarIntegrations (20250620132748)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (0.9ms)[0m  [1m[35mCREATE TABLE "calendar_integrations" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "provider" varchar NOT NULL, "provider_user_id" varchar, "access_token" varchar NOT NULL, "refresh_token" varchar, "calendar_id" varchar, "calendar_name" varchar, "token_expires_at" datetime(6), "active" boolean DEFAULT 1, "sync_appointments" boolean DEFAULT 1, "sync_reminders" boolean DEFAULT 1, "last_sync_at" datetime(6), "sync_settings" json, "provider_metadata" json, "sync_errors" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_03c9b4745a"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_calendar_integrations_on_user_id" ON "calendar_integrations" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_calendar_integrations_on_provider" ON "calendar_integrations" ("provider") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_calendar_integrations_on_active" ON "calendar_integrations" ("active") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_calendar_integrations_on_last_sync_at" ON "calendar_integrations" ("last_sync_at") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_calendar_integrations_on_user_id_and_provider" ON "calendar_integrations" ("user_id", "provider") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132748') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.7ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateOrders (20250620132817)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (2.3ms)[0m  [1m[35mCREATE TABLE "orders" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "order_number" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "subtotal_amount" decimal(10,2) NOT NULL, "tax_amount" decimal(10,2) DEFAULT 0.0, "discount_amount" decimal(10,2) DEFAULT 0.0, "total_amount" decimal(10,2) NOT NULL, "currency" varchar DEFAULT 'TRY' NOT NULL, "order_type" varchar, "billing_address" json, "discount_code" varchar, "notes" text, "confirmed_at" datetime(6), "shipped_at" datetime(6), "delivered_at" datetime(6), "cancelled_at" datetime(6), "cancellation_reason" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_f868b47f6a"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE INDEX "index_orders_on_user_id" ON "orders" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_orders_on_order_number" ON "orders" ("order_number") /*application='Myapp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE INDEX "index_orders_on_status" ON "orders" ("status") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_orders_on_order_type" ON "orders" ("order_type") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_orders_on_confirmed_at" ON "orders" ("confirmed_at") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_orders_on_user_id_and_status" ON "orders" ("user_id", "status") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132817') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.8ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateOrderItems (20250620132848)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.0ms)[0m  [1m[35mCREATE TABLE "order_items" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "order_id" integer NOT NULL, "orderable_type" varchar NOT NULL, "orderable_id" integer NOT NULL, "quantity" integer DEFAULT 1 NOT NULL, "unit_price" decimal(10,2) NOT NULL, "total_price" decimal(10,2) NOT NULL, "item_name" varchar NOT NULL, "item_description" text, "item_metadata" json, "discount_amount" decimal(10,2) DEFAULT 0.0, "discount_type" varchar, "is_refundable" boolean DEFAULT 1, "delivered_at" datetime(6), "status" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_e3cb28f071"
FOREIGN KEY ("order_id")
  REFERENCES "orders" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_order_items_on_order_id" ON "order_items" ("order_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_order_items_on_orderable" ON "order_items" ("orderable_type", "orderable_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_order_items_on_orderable_type_and_orderable_id" ON "order_items" ("orderable_type", "orderable_id") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_order_items_on_status" ON "order_items" ("status") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.3ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132848') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreatePayments (20250620132917)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.1ms)[0m  [1m[35mCREATE TABLE "payments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "order_id" integer NOT NULL, "payment_method_id" integer NOT NULL, "amount" decimal(10,2) NOT NULL, "currency" varchar DEFAULT 'TRY' NOT NULL, "status" integer DEFAULT 0 NOT NULL, "transaction_id" varchar, "gateway_transaction_id" varchar, "payment_gateway" varchar, "processed_at" datetime(6), "failed_at" datetime(6), "refunded_at" datetime(6), "refunded_amount" decimal(10,2) DEFAULT 0.0, "failure_reason" text, "gateway_response" text, "gateway_metadata" json, "receipt_url" varchar, "is_test" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_6af949464b"
FOREIGN KEY ("order_id")
  REFERENCES "orders" ("id")
, CONSTRAINT "fk_rails_d384ec1ebf"
FOREIGN KEY ("payment_method_id")
  REFERENCES "payment_methods" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_payments_on_order_id" ON "payments" ("order_id") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_payments_on_payment_method_id" ON "payments" ("payment_method_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_payments_on_transaction_id" ON "payments" ("transaction_id") /*application='Myapp'*/[0m
  [1m[35m (0.9ms)[0m  [1m[35mCREATE INDEX "index_payments_on_gateway_transaction_id" ON "payments" ("gateway_transaction_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_status" ON "payments" ("status") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_processed_at" ON "payments" ("processed_at") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_payment_gateway" ON "payments" ("payment_gateway") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_order_id_and_status" ON "payments" ("order_id", "status") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620132917') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (1.5ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.3ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  [1m[36mVideoProgress Delete All (0.2ms)[0m  [1m[31mDELETE FROM "video_progresses" /*application='Myapp'*/[0m
