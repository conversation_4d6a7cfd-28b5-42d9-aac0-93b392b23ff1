Started GET "/" for 127.0.0.1 at 2025-06-20 15:46:13 +0300
  [1m[35m (3.8ms)[0m  [1m[35mCREATE TABLE "schema_migrations" ("version" varchar NOT NULL PRIMARY KEY) /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE TABLE "ar_internal_metadata" ("key" varchar NOT NULL PRIMARY KEY, "value" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.3ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
Processing by Rails::WelcomeController#index as HTML
  Rendering /home/<USER>/.rbenv/versions/3.3.0/lib/ruby/gems/3.3.0/gems/railties-8.0.2/lib/rails/templates/rails/welcome/index.html.erb
  Rendered /home/<USER>/.rbenv/versions/3.3.0/lib/ruby/gems/3.3.0/gems/railties-8.0.2/lib/rails/templates/rails/welcome/index.html.erb (Duration: 0.7ms | GC: 0.0ms)
Completed 200 OK in 22ms (Views: 5.5ms | ActiveRecord: 0.0ms (0 queries, 0 cached) | GC: 0.7ms)


Started GET "/icon.png" for 127.0.0.1 at 2025-06-20 15:46:14 +0300
  
ActionController::RoutingError (No route matches [GET] "/icon.png"):
  
Started GET "/icon.png" for 127.0.0.1 at 2025-06-20 15:46:14 +0300
  
ActionController::RoutingError (No route matches [GET] "/icon.png"):
  
  [1m[36mActiveRecord::InternalMetadata Load (0.3ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='Myapp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Create (1.7ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('environment', 'development', '2025-06-20 12:58:34.683482', '2025-06-20 12:58:34.683485') RETURNING "key" /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
Migrating to CreateUsers (**************)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE TABLE "users" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "email" varchar NOT NULL, "password_digest" varchar NOT NULL, "first_name" varchar NOT NULL, "last_name" varchar NOT NULL, "phone_number" varchar NOT NULL, "birth_date" date NOT NULL, "verified_at" datetime(6), "locale" varchar DEFAULT 'tr', "timezone" varchar DEFAULT 'Europe/Istanbul', "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_email" ON "users" ("email") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_users_on_phone_number" ON "users" ("phone_number") /*application='Myapp'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_users_on_verified_at" ON "users" ("verified_at") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.3ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('**************') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateAdmins (20250620125502)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.3ms)[0m  [1m[35mCREATE TABLE "admins" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "role" varchar DEFAULT 'admin' NOT NULL, "permissions" text, "department" varchar NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_378b9734e4"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_admins_on_user_id" ON "admins" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_admins_on_role" ON "admins" ("role") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_admins_on_user_id" ON "admins" ("user_id") /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[31mROLLBACK TRANSACTION /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (1.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.3ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.3ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.4ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
Migrating to CreateUsers (**************)
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35mSQL (0.2ms)[0m  [1m[35mDROP INDEX "index_users_on_verified_at" /*application='Myapp'*/[0m
  [1m[35mSQL (0.7ms)[0m  [1m[35mDROP INDEX "index_users_on_phone_number" /*application='Myapp'*/[0m
  [1m[35mSQL (0.5ms)[0m  [1m[35mDROP INDEX "index_users_on_email" /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mDROP TABLE "users" /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Destroy (0.2ms)[0m  [1m[31mDELETE FROM "schema_migrations" WHERE "schema_migrations"."version" = '**************' /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (9.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.5ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
Migrating to CreateUsers (**************)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (0.7ms)[0m  [1m[35mCREATE TABLE "users" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "email" varchar NOT NULL, "password_digest" varchar NOT NULL, "first_name" varchar NOT NULL, "last_name" varchar NOT NULL, "phone_number" varchar NOT NULL, "birth_date" date NOT NULL, "verified_at" datetime(6), "locale" varchar DEFAULT 'tr', "timezone" varchar DEFAULT 'Europe/Istanbul', "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_email" ON "users" ("email") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_users_on_phone_number" ON "users" ("phone_number") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_users_on_verified_at" ON "users" ("verified_at") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('**************') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (4.2ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateAdmins (20250620125502)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (0.8ms)[0m  [1m[35mCREATE TABLE "admins" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "role" varchar DEFAULT 'admin' NOT NULL, "permissions" text, "department" varchar NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_378b9734e4"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_admins_on_user_id" ON "admins" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_admins_on_role" ON "admins" ("role") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620125502') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateDoctors (20250620125511)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (3.1ms)[0m  [1m[35mCREATE TABLE "doctors" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "license_number" varchar NOT NULL, "specialization" varchar NOT NULL, "sub_specializations" text, "graduated_university" varchar NOT NULL, "graduation_year" integer NOT NULL, "current_institution" varchar, "years_experience" integer DEFAULT 0, "consultation_fee_day" decimal(8,2) NOT NULL, "consultation_fee_night" decimal(8,2) NOT NULL, "consultation_fee_emergency" decimal(8,2) NOT NULL, "languages_spoken" text, "bio" text, "verification_status" integer DEFAULT 0, "rating" decimal(3,2), "total_consultations" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_899b01ef33"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (1.2ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_user_id" ON "doctors" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_doctors_on_license_number" ON "doctors" ("license_number") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_specialization" ON "doctors" ("specialization") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_verification_status" ON "doctors" ("verification_status") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_rating" ON "doctors" ("rating") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.2ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620125511') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreatePatients (20250620125518)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.2ms)[0m  [1m[35mCREATE TABLE "patients" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "medical_record_number" varchar NOT NULL, "emergency_contact_name" varchar NOT NULL, "emergency_contact_phone" varchar NOT NULL, "blood_type" varchar, "pregnancy_count" integer DEFAULT 0, "birth_count" integer DEFAULT 0, "smoking_status" integer DEFAULT 0, "alcohol_consumption" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_623f05c630"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_patients_on_user_id" ON "patients" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_patients_on_medical_record_number" ON "patients" ("medical_record_number") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_patients_on_blood_type" ON "patients" ("blood_type") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620125518') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
Started GET "/" for 127.0.0.1 at 2025-06-20 15:59:47 +0300
  [1m[36mActiveRecord::SchemaMigration Load (0.3ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  
ActiveRecord::PendingMigrationError (

Migrations are pending. To resolve this issue, run:

        bin/rails db:migrate

You have 1 pending migration:

db/migrate/20250620125947_create_membership_features.rb


):
  
activerecord (8.0.2) lib/active_record/migration.rb:743:in `check_pending_migrations'
activerecord (8.0.2) lib/active_record/migration.rb:660:in `block (2 levels) in call'
activesupport (8.0.2) lib/active_support/file_update_checker.rb:85:in `execute'
activesupport (8.0.2) lib/active_support/file_update_checker.rb:95:in `execute_if_updated'
activerecord (8.0.2) lib/active_record/migration.rb:667:in `block in call'
activerecord (8.0.2) lib/active_record/migration.rb:657:in `synchronize'
activerecord (8.0.2) lib/active_record/migration.rb:657:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/callbacks.rb:31:in `block in call'
activesupport (8.0.2) lib/active_support/callbacks.rb:100:in `run_callbacks'
actionpack (8.0.2) lib/action_dispatch/middleware/callbacks.rb:30:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/executor.rb:16:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/actionable_exceptions.rb:18:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/debug_exceptions.rb:31:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/show_exceptions.rb:32:in `call'
railties (8.0.2) lib/rails/rack/logger.rb:41:in `call_app'
railties (8.0.2) lib/rails/rack/logger.rb:29:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/remote_ip.rb:96:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/request_id.rb:34:in `call'
rack (3.1.16) lib/rack/runtime.rb:24:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/server_timing.rb:61:in `block in call'
actionpack (8.0.2) lib/action_dispatch/middleware/server_timing.rb:26:in `collect_events'
actionpack (8.0.2) lib/action_dispatch/middleware/server_timing.rb:60:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/executor.rb:16:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/static.rb:27:in `call'
rack (3.1.16) lib/rack/sendfile.rb:114:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/host_authorization.rb:143:in `call'
railties (8.0.2) lib/rails/engine.rb:535:in `call'
puma (6.6.0) lib/puma/configuration.rb:279:in `call'
puma (6.6.0) lib/puma/request.rb:99:in `block in handle_request'
puma (6.6.0) lib/puma/thread_pool.rb:390:in `with_force_shutdown'
puma (6.6.0) lib/puma/request.rb:98:in `handle_request'
puma (6.6.0) lib/puma/server.rb:472:in `process_client'
puma (6.6.0) lib/puma/server.rb:254:in `block in run'
puma (6.6.0) lib/puma/thread_pool.rb:167:in `block in spawn_thread'
Started GET "/favicon.ico" for 127.0.0.1 at 2025-06-20 15:59:47 +0300
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
  
ActiveRecord::PendingMigrationError (

Migrations are pending. To resolve this issue, run:

        bin/rails db:migrate

You have 1 pending migration:

db/migrate/20250620125947_create_membership_features.rb


):
  
activerecord (8.0.2) lib/active_record/migration.rb:743:in `check_pending_migrations'
activerecord (8.0.2) lib/active_record/migration.rb:660:in `block (2 levels) in call'
activesupport (8.0.2) lib/active_support/file_update_checker.rb:85:in `execute'
activerecord (8.0.2) lib/active_record/migration.rb:665:in `block in call'
activerecord (8.0.2) lib/active_record/migration.rb:657:in `synchronize'
activerecord (8.0.2) lib/active_record/migration.rb:657:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/callbacks.rb:31:in `block in call'
activesupport (8.0.2) lib/active_support/callbacks.rb:100:in `run_callbacks'
actionpack (8.0.2) lib/action_dispatch/middleware/callbacks.rb:30:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/executor.rb:16:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/actionable_exceptions.rb:18:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/debug_exceptions.rb:31:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/show_exceptions.rb:32:in `call'
railties (8.0.2) lib/rails/rack/logger.rb:41:in `call_app'
railties (8.0.2) lib/rails/rack/logger.rb:29:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/remote_ip.rb:96:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/request_id.rb:34:in `call'
rack (3.1.16) lib/rack/runtime.rb:24:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/server_timing.rb:61:in `block in call'
actionpack (8.0.2) lib/action_dispatch/middleware/server_timing.rb:26:in `collect_events'
actionpack (8.0.2) lib/action_dispatch/middleware/server_timing.rb:60:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/executor.rb:16:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/static.rb:27:in `call'
rack (3.1.16) lib/rack/sendfile.rb:114:in `call'
actionpack (8.0.2) lib/action_dispatch/middleware/host_authorization.rb:143:in `call'
railties (8.0.2) lib/rails/engine.rb:535:in `call'
puma (6.6.0) lib/puma/configuration.rb:279:in `call'
puma (6.6.0) lib/puma/request.rb:99:in `block in handle_request'
puma (6.6.0) lib/puma/thread_pool.rb:390:in `with_force_shutdown'
puma (6.6.0) lib/puma/request.rb:98:in `handle_request'
puma (6.6.0) lib/puma/server.rb:472:in `process_client'
puma (6.6.0) lib/puma/server.rb:254:in `block in run'
puma (6.6.0) lib/puma/thread_pool.rb:167:in `block in spawn_thread'
  [1m[36mUser Load (5.3ms)[0m  [1m[34mSELECT "users".* FROM "users" /* loading for pp */ LIMIT 11 /*application='Myapp'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
Migrating to CreateMembershipFeatures (20250620125947)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE TABLE "membership_features" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "feature_key" varchar NOT NULL, "description" text NOT NULL, "feature_type" integer DEFAULT 0 NOT NULL, "category" varchar NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_membership_features_on_feature_key" ON "membership_features" ("feature_key") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_membership_features_on_category" ON "membership_features" ("category") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_membership_features_on_feature_type" ON "membership_features" ("feature_type") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620125947') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (4.6ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateMembershipTiers (20250620125956)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (1.0ms)[0m  [1m[35mCREATE TABLE "membership_tiers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "slug" varchar NOT NULL, "description" text NOT NULL, "price" decimal(8,2) DEFAULT 0.0 NOT NULL, "currency" varchar DEFAULT 'TRY' NOT NULL, "billing_cycle" integer DEFAULT 1 NOT NULL, "trial_days" integer DEFAULT 0, "sort_order" integer NOT NULL, "active" boolean DEFAULT 1, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_membership_tiers_on_slug" ON "membership_tiers" ("slug") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_membership_tiers_on_active" ON "membership_tiers" ("active") /*application='Myapp'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_membership_tiers_on_sort_order" ON "membership_tiers" ("sort_order") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_membership_tiers_on_price" ON "membership_tiers" ("price") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620125956') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateTierFeatures (20250620130013)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (6.6ms)[0m  [1m[35mCREATE TABLE "tier_features" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "membership_tier_id" integer NOT NULL, "membership_feature_id" integer NOT NULL, "limit_value" integer, "is_unlimited" boolean DEFAULT 0, "is_included" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_39701094c9"
FOREIGN KEY ("membership_tier_id")
  REFERENCES "membership_tiers" ("id")
, CONSTRAINT "fk_rails_605fb37803"
FOREIGN KEY ("membership_feature_id")
  REFERENCES "membership_features" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_membership_tier_id" ON "tier_features" ("membership_tier_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_membership_feature_id" ON "tier_features" ("membership_feature_id") /*application='Myapp'*/[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_tier_features_on_tier_and_feature" ON "tier_features" ("membership_tier_id", "membership_feature_id") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_is_included" ON "tier_features" ("is_included") /*application='Myapp'*/[0m
  [1m[35m (1.2ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_is_unlimited" ON "tier_features" ("is_unlimited") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.7ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620130013') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.9ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateUserMemberships (20250620130022)
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (2.7ms)[0m  [1m[35mCREATE TABLE "user_memberships" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "membership_tier_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "starts_at" datetime(6) NOT NULL, "expires_at" datetime(6) NOT NULL, "auto_renewal" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_e670a4dd3b"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
, CONSTRAINT "fk_rails_a2f75c886b"
FOREIGN KEY ("membership_tier_id")
  REFERENCES "membership_tiers" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_user_id" ON "user_memberships" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (1.0ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_membership_tier_id" ON "user_memberships" ("membership_tier_id") /*application='Myapp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_status" ON "user_memberships" ("status") /*application='Myapp'*/[0m
  [1m[35m (0.7ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_starts_at" ON "user_memberships" ("starts_at") /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_expires_at" ON "user_memberships" ("expires_at") /*application='Myapp'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_user_id_and_status" ON "user_memberships" ("user_id", "status") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.5ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620130022') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.6ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
Migrating to CreateUsageTrackers (20250620130032)
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='Myapp'*/[0m
  [1m[35m (4.1ms)[0m  [1m[35mCREATE TABLE "usage_trackers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "membership_feature_id" integer NOT NULL, "period_start" date NOT NULL, "period_end" date NOT NULL, "usage_count" integer DEFAULT 0, "limit_exceeded" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_d413b57732"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
, CONSTRAINT "fk_rails_4cba118828"
FOREIGN KEY ("membership_feature_id")
  REFERENCES "membership_features" ("id")
) /*application='Myapp'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_user_id" ON "usage_trackers" ("user_id") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_membership_feature_id" ON "usage_trackers" ("membership_feature_id") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_usage_trackers_unique" ON "usage_trackers" ("user_id", "membership_feature_id", "period_start") /*application='Myapp'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_period_start" ON "usage_trackers" ("period_start") /*application='Myapp'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_limit_exceeded" ON "usage_trackers" ("limit_exceeded") /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.4ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250620130032') RETURNING "version" /*application='Myapp'*/[0m
  [1m[36mTRANSACTION (0.5ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='Myapp'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='Myapp'*/[0m
